import sys
import os
import json
import uuid
import copy
from dataclasses import dataclass, field, asdict
from functools import partial
from typing import Optional, List, Any
import shutil
from datetime import datetime, timedelta
import logging
from pathlib import Path
# Removed sync server imports - desktop-only application

# --- DLL Directory Setup for Windows (MUST BE FIRST) ---
if sys.platform == "win32":
    try:
        # Check for Dependencies folder (standalone mode) or onefile mode
        exe_dir = Path(sys.executable).parent
        dependencies_dir = exe_dir / "Dependencies"

        # Debug output (will be visible in console if run from terminal)
        print(f"[DLL Setup] Executable: {sys.executable}")
        print(f"[DLL Setup] Frozen: {getattr(sys, 'frozen', 'NOT SET')}")
        print(f"[DLL Setup] Dependencies dir: {dependencies_dir}")
        print(f"[DLL Setup] Dependencies exists: {dependencies_dir.exists()}")

        if dependencies_dir.exists():
            # Standalone mode - set up DLL paths to Dependencies folder
            print(f"[DLL Setup] Standalone mode - setting up DLL paths...")

            # Method 1: Add to PATH environment variable (most reliable)
            try:
                current_path = os.environ.get('PATH', '')
                if str(dependencies_dir) not in current_path:
                    os.environ['PATH'] = str(dependencies_dir) + os.pathsep + current_path
                    print(f"[DLL Setup] Added to PATH: {dependencies_dir}")
            except Exception as e:
                print(f"[DLL Setup] PATH failed: {e}")

            # Method 2: Try os.add_dll_directory (Python 3.8+)
            if hasattr(os, 'add_dll_directory'):
                try:
                    os.add_dll_directory(str(dependencies_dir))
                    print(f"[DLL Setup] os.add_dll_directory successful")
                except Exception as e:
                    print(f"[DLL Setup] os.add_dll_directory failed: {e}")

            # Method 3: Fallback to SetDllDirectory (skip if ctypes fails)
            try:
                import ctypes
                result = ctypes.windll.kernel32.SetDllDirectoryW(str(dependencies_dir))
                print(f"[DLL Setup] SetDllDirectoryW result: {result}")
            except ImportError as e:
                print(f"[DLL Setup] ctypes import failed (known Nuitka issue): {e}")
            except Exception as e:
                print(f"[DLL Setup] SetDllDirectoryW failed: {e}")
        else:
            print(f"[DLL Setup] Onefile mode or development - no DLL setup needed")

    except Exception as e:
        print(f"[DLL Setup] Exception: {e}")
        # Continue anyway

# --- PySide6 & Qtawesome Imports (AFTER DLL setup) ---
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QScrollArea, QLabel,
    QHBoxLayout, QDialog, QFormLayout, QLineEdit, QSpinBox, QDialogButtonBox,
    QMessageBox, QColorDialog, QPushButton, QSpacerItem, QSizePolicy, QGridLayout,
    QStackedWidget, QFrame, QDoubleSpinBox, QFileDialog, QComboBox, QCheckBox, QMenu,
    QTextEdit, QRadioButton, QListWidget, QListWidgetItem, QScrollBar
)
from PySide6.QtGui import QColor, QPixmap, QPainter, QPainterPath, QIcon, QFont, QMouseEvent, QPen, QDrag
from PySide6.QtCore import (
    Qt, QSize, Signal, QPropertyAnimation, QEasingCurve, QRectF, QRect, QPoint,
    QParallelAnimationGroup, QAbstractAnimation, QTimer, QObject, QEvent, QSequentialAnimationGroup,
    QThread, Slot, QMimeData
)

import qtawesome as qta

# PERFORMANCE OPTIMIZATION: Icon cache to avoid repeated file I/O
_icon_cache = {}

# Logging-Konfiguration (after PySide6 imports)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)
logger = logging.getLogger(__name__)

# --- Windows Blur Effect Imports ---
# Removed for research - will be reimplemented later

# --- Skalierungsfaktor & UI-Konstanten ---
SCALE_FACTOR = 1.0
BASE_FONT_SIZE = 11; BASE_ICON_SIZE = 20; BASE_CIRCLE_SIZE = 18; BASE_MARGIN = 15
BASE_SPACING = 8; BASE_RADIUS = 15; BASE_ACTION_BTN_SIZE = 44; BASE_ROW_ACTION_BTN_SIZE = 36

# --- Pfad-Funktionen für portable Version ---
def get_app_directory() -> Path:
    """
    Gibt das Verzeichnis zurück, in dem die Anwendung ausgeführt wird.
    Für portable Version: Verzeichnis der .exe, sonst das Verzeichnis der main.py.
    Unterstützt sowohl PyInstaller als auch Nuitka.
    """
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller bundle: _MEIPASS points to temp extraction folder
        # But we want the actual app directory where the .exe is
        exe_dir = Path(sys.executable).parent
        # Check if we're in _internal (launched by wrapper)
        if exe_dir.name == "_internal":
            return exe_dir.parent
        return exe_dir
    elif hasattr(sys, 'frozen') and sys.frozen:
        # Nuitka or other frozen executable
        exe_dir = Path(sys.executable).parent
        # Check if we're inside Dependencies folder
        if exe_dir.name == "Dependencies":
            return exe_dir.parent
        return exe_dir
    elif sys.executable.endswith('.exe') and 'python' not in sys.executable.lower():
        # Nuitka onefile mode: For data files, we want the directory where the actual .exe is
        # Get the original executable path from command line or environment
        import os

        # Try to get the real executable path
        if hasattr(sys, 'argv') and sys.argv and sys.argv[0].endswith('.exe'):
            # Use the path from argv[0] if it's an .exe
            real_exe_path = Path(sys.argv[0]).resolve()
            print(f"[App Dir] Using argv[0] path: {real_exe_path.parent}")
            return real_exe_path.parent

        # Fallback to sys.executable directory
        exe_dir = Path(sys.executable).parent
        print(f"[App Dir] Using sys.executable path: {exe_dir}")
        return exe_dir
    else:
        # In der Entwicklung
        return Path(__file__).resolve().parent

def get_data_path() -> Path:
    """
    Gibt den Pfad zum Datenverzeichnis zurück.
    In compiled version: Data/ folder, in development: data/ folder
    """
    app_dir = get_app_directory()

    # Check if we're in compiled mode by looking for the actual executable name
    if sys.executable.endswith('HueStock.exe') or (hasattr(sys, 'frozen') and sys.frozen):
        # Compiled version - use Data folder
        data_path = app_dir / "Data"
        print(f"[Data Path] Compiled mode detected, using: {data_path}")
        return data_path
    elif (app_dir / "Dependencies").exists():
        # Legacy compiled structure with Dependencies folder
        return app_dir / "Data"
    elif str(app_dir).endswith('HueStock') and 'Temp' in str(app_dir):
        # Running from unified app but in temp directory - use actual working directory
        working_dir = Path.cwd()
        data_path = working_dir / "data"
        print(f"[Data Path] Unified app mode, using working directory: {data_path}")
        return data_path
    else:
        # Development mode - use data folder
        data_path = app_dir / "data"
        print(f"[Data Path] Development mode, using: {data_path}")
        return data_path



def get_svgs_path() -> Path:
    """
    Gibt den Pfad zum SVGs-Verzeichnis zurück - aus PyInstaller/Nuitka bundle oder development.
    """
    app_dir = get_app_directory()

    if hasattr(sys, '_MEIPASS'):
        # PyInstaller bundle - SVGs sind im temporären Verzeichnis
        return Path(sys._MEIPASS) / "svgs"
    elif (app_dir / "Dependencies").exists():
        # Compiled version with Dependencies folder - SVGs are in Dependencies
        return app_dir / "Dependencies" / "svgs"
    elif hasattr(sys, 'frozen') and sys.frozen:
        # Nuitka frozen executable - check multiple possible locations
        possible_paths = [
            # Nuitka onefile temp extraction directory
            Path(os.environ.get('TEMP', '')) / "HueStock" / "svgs",
            # Standard Nuitka location
            Path(sys.executable).parent / "svgs",
            # Alternative temp location
            Path(sys.executable).parent / "HueStock" / "svgs"
        ]

        for path in possible_paths:
            if path.exists() and path.is_dir():
                print(f"[SVG] Found SVGs at: {path}")
                return path

        # Fallback - return first path even if it doesn't exist (will trigger fallback icons)
        print(f"[SVG] No SVG directory found, using fallback: {possible_paths[0]}")
        return possible_paths[0]
    elif sys.executable.endswith('.exe') and 'python' not in sys.executable.lower():
        # Nuitka onefile mode - SVGs are embedded, use temp extraction path
        return Path(sys.executable).parent / "svgs"
    else:
        # In der Entwicklung
        return Path(__file__).resolve().parent / "svgs"

def load_svg_icon(icon_name: str, color: str = 'white') -> QIcon:
    """
    PERFORMANCE OPTIMIZATION: Cached SVG icon loading.
    Lädt ein SVG-Icon aus dem svgs-Verzeichnis (embedded in PyInstaller bundle).
    """
    # Check cache first
    cache_key = f"{icon_name}_{color}"
    if cache_key in _icon_cache:
        return _icon_cache[cache_key]

    try:
        svg_path = get_svgs_path() / f"{icon_name}.svg"
        if svg_path.exists():
            # Just load the SVG directly - they should already be white or we'll make them white manually
            icon = QIcon(str(svg_path))
        else:
            # Debug: List available SVGs to help diagnose missing icons
            svgs_dir = get_svgs_path()
            if svgs_dir.exists():
                available_svgs = [f.stem for f in svgs_dir.glob("*.svg")]
                print(f"[SVG] Icon '{icon_name}' not found. Available: {available_svgs}")
            else:
                print(f"[SVG] SVGs directory not found: {svgs_dir}")

            logger.warning(f"SVG icon not found: {svg_path}")
            # Fallback zu qtawesome mit korrekten Icon-Namen
            fallback_icons = {
                'list-filter-plus': 'filter',
                'bell-plus': 'bell',
                'arrow-left': 'arrow-left',
                'chart-pie': 'chart-pie',
                'settings-2': 'cog',
                'square-pen': 'edit',
                'printer-check': 'print',
                'trash': 'trash',
                'store': 'store',
                'history': 'history',
                'book-open-text': 'book',
                'package-plus': 'plus',
                'package-minus': 'minus'
            }
            fallback_name = fallback_icons.get(icon_name, 'question-circle')
            icon = qta.icon(f'fa5s.{fallback_name}', color=color)
    except Exception as e:
        logger.error(f"Error loading SVG icon {icon_name}: {e}")
        # Ultimate fallback
        icon = qta.icon('fa5s.question-circle', color=color)

    # Cache the icon for future use
    _icon_cache[cache_key] = icon
    return icon

def get_products_path() -> Path:
    """
    Gibt den Pfad zum Products-Verzeichnis zurück.
    In compiled version: Data/Products/ folder, in development: data/products/ folder
    """
    data_dir = get_data_path()
    return data_dir / "products"

def get_appicon_path() -> Path:
    """
    Gibt den Pfad zur appicon.png zurück - prüft sowohl embedded als auch externe Pfade.
    Unterstützt PyInstaller und Nuitka.
    """
    # Try embedded location first (PyInstaller bundle)
    if hasattr(sys, '_MEIPASS'):
        embedded_path = Path(sys._MEIPASS) / "appicon.png"
        if embedded_path.exists():
            return embedded_path

    # Try Nuitka embedded location
    elif hasattr(sys, 'frozen') and sys.frozen:
        nuitka_path = Path(sys.executable).parent / "appicon.png"
        if nuitka_path.exists():
            return nuitka_path

    # Try external locations
    external_paths = [
        get_app_directory() / "appicon.png",
        Path(__file__).parent / "appicon.png"
    ]

    for path in external_paths:
        if path.exists():
            return path

    # Return default path even if it doesn't exist (for error handling)
    return get_app_directory() / "appicon.png"



def copy_initial_files() -> None:
    """
    Kopiert initiale Dateien aus der .exe in das Programmverzeichnis (nur beim ersten Start).
    Stellt sicher, dass Data und Data/products Verzeichnisse immer existieren.
    Unterstützt sowohl PyInstaller als auch Nuitka.
    """
    app_dir = get_app_directory()
    data_dir = get_data_path()
    products_dir = get_products_path()

    print(f"[Copy Files] Ensuring directories exist:")
    print(f"[Copy Files] Data dir: {data_dir}")
    print(f"[Copy Files] Products dir: {products_dir}")

    # Verzeichnisse erstellen (immer, auch wenn sie schon existieren)
    try:
        data_dir.mkdir(parents=True, exist_ok=True)
        products_dir.mkdir(parents=True, exist_ok=True)
        print(f"[Copy Files] Directories created/verified successfully")
    except (OSError, PermissionError) as e:
        logger.error(f"Fehler beim Erstellen der Verzeichnisse: {e}")
        print(f"[Copy Files] ERROR: Could not create directories: {e}")
        return

    # Dateien aus der .exe extrahieren (nur wenn sie noch nicht existieren)
    try:
        # Determine source paths based on packaging method
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller bundle
            products_source = Path(sys._MEIPASS) / "products"
            data_source = Path(sys._MEIPASS) / "data_template"
        elif (app_dir / "Dependencies").exists():
            # Compiled version with Dependencies folder
            products_source = app_dir / "Dependencies" / "products"
            data_source = app_dir / "Dependencies" / "data_template"
        elif hasattr(sys, 'frozen') and sys.frozen:
            # Nuitka onefile mode - resources are extracted to temp directory
            import tempfile
            temp_dir = Path(tempfile.gettempdir()) / "HueStock"
            products_source = temp_dir / "data_template" / "products"
            data_source = temp_dir / "data_template"
            print(f"[Copy Files] Looking for template data in: {data_source}")
        elif sys.executable.endswith('.exe') and 'python' not in sys.executable.lower():
            # Nuitka onefile mode - resources are extracted to temp directory
            import tempfile
            temp_dir = Path(tempfile.gettempdir()) / "HueStock"
            products_source = temp_dir / "data_template" / "products"
            data_source = temp_dir / "data_template"
            print(f"[Copy Files] Looking for template data in: {data_source}")
        else:
            # Development mode
            products_source = Path("data") / "products"
            data_source = Path("data")

        # Products kopieren
        if products_source.exists() and products_dir.exists():
            try:
                if not any(products_dir.iterdir()):
                    for file in products_source.iterdir():
                        if file.is_file():
                            try:
                                shutil.copy2(file, products_dir / file.name)
                            except (OSError, PermissionError, shutil.SameFileError) as e:
                                logger.warning(f"Konnte Product-Datei {file.name} nicht kopieren: {e}")
            except (OSError, PermissionError) as e:
                logger.warning(f"Konnte Products-Verzeichnis nicht lesen: {e}")

        # Data Dateien kopieren
        if data_source.exists() and data_dir.exists():
            for file in ["filaments.json", "settings.json", "products.json"]:
                src_file = data_source / file
                dest_file = data_dir / file
                if src_file.exists() and not dest_file.exists():
                    try:
                        shutil.copy2(src_file, dest_file)
                    except (OSError, PermissionError, shutil.SameFileError) as e:
                        logger.warning(f"Konnte Datei {file} nicht kopieren: {e}")

    except Exception as e:
        logger.error(f"Unerwarteter Fehler beim Kopieren der initialen Dateien: {e}")

# --- Windows Blur Effect Utilities ---
# Blur functionality removed for research and future reimplementation

# --- Removed HTTP Server for Companion App Sync ---
# All sync server functionality removed for desktop-only application










# --- Theme-Definitionen ---
THEMES = {
    "Dark Modern": {
        "name": "Dark Modern",
        "background": "#2D2D2D",
        "surface": "#3D3D3D",
        "primary": "#17a2b8",
        "success": "#28a745",
        "warning": "#f0ad4e",
        "danger": "#dc3545",
        "text": "#E0E0E0",
        "text_muted": "#B0B0B0",
        "border": "#555",
        "hover": "#4A4A4A",
        "is_light": False,
        # Extended theme properties for full theming
        "notification_btn": "#50A0D1",
        "shop_btn": "#1E91D6",
        "settings_btn": "#0072BB",
        "history_btn": "#6c757d",
        "calculator_bg": "#2D2D2D",
        "calculator_border": "#4A4A4A",
        "calculator_display": "#1E1E1E",
        "calculator_num_btn": "#4A4A4A",
        "calculator_num_btn_hover": "#5A5A5A",
        "calculator_num_btn_pressed": "#3A3A3A",
        "calculator_clear_btn": "#e74c3c",
        "calculator_clear_border": "#c0392b",
        "calculator_back_btn": "#f39c12",
        "calculator_back_border": "#e67e22",
        "calculator_stock_btn": "#27ae60",
        "calculator_stock_border": "#229954",
        "calculator_usage_btn": "#e74c3c",
        "calculator_usage_border": "#c0392b",
        "calculator_comma_btn": "#2D2D2D",
        "calculator_comma_border": "#4A4A4A",
        "notification_red": "#942421",
        "notification_yellow": "#A87900",
        "log_print_btn": "rgba(0, 0, 0, 0.5)",
        "log_print_border": "rgba(255, 255, 255, 0.7)",
        "log_print_hover": "rgba(0, 0, 0, 0.7)"
    },

    "Ocean Deep": {
        "name": "Ocean Deep",
        "background": "#0a1929",
        "surface": "#1e293b",
        "primary": "#0ea5e9",
        "success": "#10b981",
        "warning": "#f59e0b",
        "danger": "#ef4444",
        "text": "#f1f5f9",
        "text_muted": "#94a3b8",
        "border": "#334155",
        "hover": "#475569",
        "is_light": False,
        # Ocean-themed button colors - deep blues and teals
        "notification_btn": "#0284c7",  # Deep sky blue
        "shop_btn": "#0891b2",          # Cyan blue
        "settings_btn": "#1e40af",      # Royal blue
        "history_btn": "#475569",       # Slate gray
        "calculator_bg": "#0a1929",
        "calculator_border": "#334155",
        "calculator_display": "#1e293b",
        "calculator_num_btn": "#475569",
        "calculator_num_btn_hover": "#64748b",
        "calculator_num_btn_pressed": "#334155",
        "calculator_clear_btn": "#ef4444",
        "calculator_clear_border": "#334155",
        "calculator_back_btn": "#f59e0b",
        "calculator_back_border": "#334155",
        "calculator_stock_btn": "#10b981",
        "calculator_stock_border": "#334155",
        "calculator_usage_btn": "#ef4444",
        "calculator_usage_border": "#334155",
        "calculator_comma_btn": "#0a1929",
        "calculator_comma_border": "#334155",
        "notification_red": "#dc2626",
        "notification_yellow": "#d97706",
        "log_print_btn": "rgba(14, 165, 233, 0.3)",
        "log_print_border": "rgba(241, 245, 249, 0.7)",
        "log_print_hover": "rgba(14, 165, 233, 0.5)"
    },

    "Forest Sage": {
        "name": "Forest Sage",
        "background": "#1a2e1a",
        "surface": "#2d4a2d",
        "primary": "#4ade80",
        "success": "#22c55e",
        "warning": "#eab308",
        "danger": "#ef4444",
        "text": "#f0fdf4",
        "text_muted": "#bbf7d0",
        "border": "#365a36",
        "hover": "#3d5a3d",
        "is_light": False,
        # Nature-inspired greens and earth tones
        "notification_btn": "#16a34a",  # Forest green
        "shop_btn": "#059669",          # Emerald
        "settings_btn": "#166534",      # Dark green
        "history_btn": "#6b7280",       # Gray
        "calculator_bg": "#1a2e1a",
        "calculator_border": "#365a36",
        "calculator_display": "#2d4a2d",
        "calculator_num_btn": "#3d5a3d",
        "calculator_num_btn_hover": "#4d6a4d",
        "calculator_num_btn_pressed": "#2d4a2d",
        "calculator_clear_btn": "#ef4444",
        "calculator_clear_border": "#365a36",
        "calculator_back_btn": "#eab308",
        "calculator_back_border": "#365a36",
        "calculator_stock_btn": "#22c55e",
        "calculator_stock_border": "#365a36",
        "calculator_usage_btn": "#ef4444",
        "calculator_usage_border": "#365a36",
        "calculator_comma_btn": "#1a2e1a",
        "calculator_comma_border": "#365a36",
        "notification_red": "#dc2626",
        "notification_yellow": "#ca8a04",
        "log_print_btn": "rgba(34, 197, 94, 0.3)",
        "log_print_border": "rgba(240, 253, 244, 0.7)",
        "log_print_hover": "rgba(34, 197, 94, 0.5)"
    },

    "Sunset Amber": {
        "name": "Sunset Amber",
        "background": "#2d1b0e",
        "surface": "#3d2b1e",
        "primary": "#f59e0b",
        "success": "#10b981",
        "warning": "#f59e0b",
        "danger": "#ef4444",
        "text": "#fef3c7",
        "text_muted": "#fbbf24",
        "border": "#92400e",
        "hover": "#451a03",
        "is_light": False,
        # Warm sunset colors - oranges and golds
        "notification_btn": "#ea580c",  # Bright orange
        "shop_btn": "#d97706",          # Amber
        "settings_btn": "#92400e",      # Dark amber
        "history_btn": "#78716c",       # Warm gray
        "calculator_bg": "#2d1b0e",
        "calculator_border": "#92400e",
        "calculator_display": "#3d2b1e",
        "calculator_num_btn": "#451a03",
        "calculator_num_btn_hover": "#92400e",
        "calculator_num_btn_pressed": "#3d2b1e",
        "calculator_clear_btn": "#ef4444",
        "calculator_clear_border": "#92400e",
        "calculator_back_btn": "#f59e0b",
        "calculator_back_border": "#92400e",
        "calculator_stock_btn": "#10b981",
        "calculator_stock_border": "#92400e",
        "calculator_usage_btn": "#ef4444",
        "calculator_usage_border": "#92400e",
        "calculator_comma_btn": "#2d1b0e",
        "calculator_comma_border": "#92400e",
        "notification_red": "#dc2626",
        "notification_yellow": "#d97706",
        "log_print_btn": "rgba(245, 158, 11, 0.3)",
        "log_print_border": "rgba(254, 243, 199, 0.7)",
        "log_print_hover": "rgba(245, 158, 11, 0.5)"
    },

    "Royal Purple": {
        "name": "Royal Purple",
        "background": "#1e1b2e",
        "surface": "#2a2438",
        "primary": "#8b5cf6",
        "success": "#10b981",
        "warning": "#f59e0b",
        "danger": "#ef4444",
        "text": "#f3f4f6",
        "text_muted": "#c4b5fd",
        "border": "#4c1d95",
        "hover": "#312e81",
        "is_light": False,
        # Rich purple and violet tones
        "notification_btn": "#7c3aed",  # Violet
        "shop_btn": "#8b5cf6",          # Purple
        "settings_btn": "#4c1d95",      # Dark purple
        "history_btn": "#6b7280",       # Gray
        "calculator_bg": "#1e1b2e",
        "calculator_border": "#4c1d95",
        "calculator_display": "#2a2438",
        "calculator_num_btn": "#312e81",
        "calculator_num_btn_hover": "#4c1d95",
        "calculator_num_btn_pressed": "#2a2438",
        "calculator_clear_btn": "#ef4444",
        "calculator_clear_border": "#4c1d95",
        "calculator_back_btn": "#f59e0b",
        "calculator_back_border": "#4c1d95",
        "calculator_stock_btn": "#10b981",
        "calculator_stock_border": "#4c1d95",
        "calculator_usage_btn": "#ef4444",
        "calculator_usage_border": "#4c1d95",
        "calculator_comma_btn": "#1e1b2e",
        "calculator_comma_border": "#4c1d95",
        "notification_red": "#dc2626",
        "notification_yellow": "#d97706",
        "log_print_btn": "rgba(139, 92, 246, 0.3)",
        "log_print_border": "rgba(243, 244, 246, 0.7)",
        "log_print_hover": "rgba(139, 92, 246, 0.5)"
    },

    "Crimson Steel": {
        "name": "Crimson Steel",
        "background": "#1a1a1a",
        "surface": "#2a2a2a",
        "primary": "#dc2626",
        "success": "#16a34a",
        "warning": "#ca8a04",
        "danger": "#dc2626",
        "text": "#f5f5f5",
        "text_muted": "#a3a3a3",
        "border": "#525252",
        "hover": "#3a3a3a",
        "is_light": False,
        # Bold red and steel gray theme
        "notification_btn": "#dc2626",  # Crimson red
        "shop_btn": "#991b1b",          # Dark red
        "settings_btn": "#525252",      # Steel gray
        "history_btn": "#6b7280",       # Gray
        "calculator_bg": "#1a1a1a",
        "calculator_border": "#525252",
        "calculator_display": "#2a2a2a",
        "calculator_num_btn": "#3a3a3a",
        "calculator_num_btn_hover": "#525252",
        "calculator_num_btn_pressed": "#2a2a2a",
        "calculator_clear_btn": "#dc2626",
        "calculator_clear_border": "#525252",
        "calculator_back_btn": "#ca8a04",
        "calculator_back_border": "#525252",
        "calculator_stock_btn": "#16a34a",
        "calculator_stock_border": "#525252",
        "calculator_usage_btn": "#dc2626",
        "calculator_usage_border": "#525252",
        "calculator_comma_btn": "#1a1a1a",
        "calculator_comma_border": "#525252",
        "notification_red": "#dc2626",
        "notification_yellow": "#ca8a04",
        "log_print_btn": "rgba(220, 38, 38, 0.3)",
        "log_print_border": "rgba(245, 245, 245, 0.7)",
        "log_print_hover": "rgba(220, 38, 38, 0.5)"
    },

    "Arctic Frost": {
        "name": "Arctic Frost",
        "background": "#0f172a",
        "surface": "#1e293b",
        "primary": "#38bdf8",
        "success": "#22d3ee",
        "warning": "#fbbf24",
        "danger": "#f87171",
        "text": "#f1f5f9",
        "text_muted": "#94a3b8",
        "border": "#334155",
        "hover": "#475569",
        "is_light": False,
        # Cool ice blue and frost tones
        "notification_btn": "#0ea5e9",  # Sky blue
        "shop_btn": "#06b6d4",          # Cyan
        "settings_btn": "#0284c7",      # Blue
        "history_btn": "#64748b",       # Slate
        "calculator_bg": "#0f172a",
        "calculator_border": "#334155",
        "calculator_display": "#1e293b",
        "calculator_num_btn": "#475569",
        "calculator_num_btn_hover": "#64748b",
        "calculator_num_btn_pressed": "#334155",
        "calculator_clear_btn": "#f87171",
        "calculator_clear_border": "#334155",
        "calculator_back_btn": "#fbbf24",
        "calculator_back_border": "#334155",
        "calculator_stock_btn": "#22d3ee",
        "calculator_stock_border": "#334155",
        "calculator_usage_btn": "#f87171",
        "calculator_usage_border": "#334155",
        "calculator_comma_btn": "#0f172a",
        "calculator_comma_border": "#334155",
        "notification_red": "#dc2626",
        "notification_yellow": "#d97706",
        "log_print_btn": "rgba(14, 165, 233, 0.3)",
        "log_print_border": "rgba(241, 245, 249, 0.7)",
        "log_print_hover": "rgba(14, 165, 233, 0.5)"
    },

    "Emerald Matrix": {
        "name": "Emerald Matrix",
        "background": "#0a1f0a",
        "surface": "#1a2f1a",
        "primary": "#10b981",
        "success": "#22c55e",
        "warning": "#eab308",
        "danger": "#ef4444",
        "text": "#ecfdf5",
        "text_muted": "#86efac",
        "border": "#166534",
        "hover": "#14532d",
        "is_light": False,
        # Matrix-inspired green theme with high contrast
        "notification_btn": "#059669",  # Emerald
        "shop_btn": "#10b981",          # Green
        "settings_btn": "#166534",      # Dark green
        "history_btn": "#6b7280",       # Gray
        "calculator_bg": "#0a1f0a",
        "calculator_border": "#166534",
        "calculator_display": "#1a2f1a",
        "calculator_num_btn": "#14532d",
        "calculator_num_btn_hover": "#166534",
        "calculator_num_btn_pressed": "#1a2f1a",
        "calculator_clear_btn": "#ef4444",
        "calculator_clear_border": "#166534",
        "calculator_back_btn": "#eab308",
        "calculator_back_border": "#166534",
        "calculator_stock_btn": "#22c55e",
        "calculator_stock_border": "#166534",
        "calculator_usage_btn": "#ef4444",
        "calculator_usage_border": "#166534",
        "calculator_comma_btn": "#0a1f0a",
        "calculator_comma_border": "#166534",
        "notification_red": "#dc2626",
        "notification_yellow": "#ca8a04",
        "log_print_btn": "rgba(16, 185, 129, 0.3)",
        "log_print_border": "rgba(236, 253, 245, 0.7)",
        "log_print_hover": "rgba(16, 185, 129, 0.5)"
    }
}



def get_theme_stylesheet(theme_name: str) -> str:
    """Generate dynamic stylesheet based on theme."""
    theme = THEMES.get(theme_name, THEMES["Dark Modern"])

    # Safely get theme values with fallbacks for extended properties
    notification_btn = theme.get('notification_btn', '#50A0D1')
    shop_btn = theme.get('shop_btn', '#1E91D6')
    settings_btn = theme.get('settings_btn', '#0072BB')
    history_btn = theme.get('history_btn', theme['border'])

    return f"""
    QMainWindow, QDialog {{ background-color: {theme['background']}; font-family: 'Segoe UI'; font-weight: bold; }}
    #titleLabel {{ font-size: {s_title_font_size}pt; font-weight: bold; color: {theme['text']}; }}
    #headerWidget {{ background-color: {theme['surface']}; border-radius: {s_radius}px; color: {theme['text_muted']}; font-weight: bold; font-size: {s_main_font_size}pt; }}
    #listFrame {{ background-color: {theme['surface']}; border-radius: {s_radius}px; }}
    #scrollArea, #scrollArea > QWidget > QWidget {{ background-color: transparent; border: none; }}
    #FilamentRow {{ background-color: transparent; color: {theme['text']}; font-size: {s_main_font_size}pt; }}
    #FilamentRow[selected="true"] {{
        background-color: {theme['hover']};
        border-radius: {s_radius - 3}px;
        margin: 1px 1px 1px 7px;
    }}
    #colorCircle, #warningCircle {{ border-radius: 6px; border: 1px solid rgba(255, 255, 255, 0.12); }}
    #addButton {{ background-color: {theme['success']}; border-radius: {s_action_btn_radius}px; border: none; }}
    #editButton {{ background-color: {theme['warning']}; border-radius: {s_action_btn_radius}px; border: none; }}
    #deleteButton {{ background-color: {theme['danger']}; border-radius: {s_action_btn_radius}px; border: none; }}
    #settingsButton {{ background-color: {settings_btn}; border-radius: {s_action_btn_radius}px; border: none; }}
    #notificationButton {{ background-color: {notification_btn}; border-radius: {s_action_btn_radius}px; border: none; }}
    #historyButton {{ background-color: {history_btn}; border-radius: {s_action_btn_radius}px; border: none; }}
    QPushButton[objectName="backButton"] {{ background-color: {theme['surface']} !important; border-radius: {s_radius}px !important; border: 2px solid {theme['border']} !important; }}
    QPushButton[objectName="backButton"]:hover {{ background-color: {theme['hover']} !important; border-color: {theme['primary']} !important; }}
    QPushButton[objectName="backButton"]:pressed {{ background-color: {theme['border']} !important; border-color: {theme['primary']} !important; }}
    QPushButton[objectName="backButton"]:focus {{ background-color: {theme['surface']} !important; border-color: {theme['primary']} !important; outline: none; }}
    #shopButton {{ background-color: {shop_btn}; border-radius: {s_action_btn_radius}px; border: none; }}
    #adjustButton {{ background-color: {theme['hover']}; border-radius: {s_row_action_btn_radius}px; }}
    #adjustButton:hover {{ background-color: {theme['border']}; }}
    #notesButton {{ background-color: {theme['hover']}; border-radius: {s_row_action_btn_radius}px; }}
    #notesButton:hover {{ background-color: {theme['border']}; }}
    #transparentButton {{ background-color: transparent; border: none; }}
    #statsBox {{ margin-bottom: 2px; }}
    #totalUsageLabel {{ font-size: {int(36 * SCALE_FACTOR)}pt; font-weight: bold; color: {theme['text']}; }}
    #funFactLabel {{ font-size: {int(12 * SCALE_FACTOR)}pt; color: {theme['text_muted']}; font-style: italic; }}
    #subTitleLabel {{ font-size: {int(11*SCALE_FACTOR)}pt; font-weight: bold; color: {theme['text']}; }}
    QLabel[class="topRankLabel"] {{ font-weight: bold; color: {theme['text_muted']}; }}
    #rightColumnContainer {{ margin-top: 15px; }}
    #productTile {{ background-color: {theme['surface']}; border-radius: 8px; border: 2px solid transparent; }}
    #productTile[selected="true"] {{ border-color: {theme['text']}; }}
    #productTile[dragHover="true"] {{ border-color: {theme['primary']}; background-color: {theme['hover']}; }}
    #productImage {{ background-color: transparent; }}
    #productTitle {{ padding: 5px; font-size: {s_main_font_size-1}pt; background-color: {theme['surface']}; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; }}
    #variationFrame {{ border: 1px solid {theme['border']}; border-radius: 5px; margin-top: 10px; padding: 5px; }}
    #logPrintButton {{ background-color: rgba(0, 0, 0, 0.5); border: 1px solid rgba(255, 255, 255, 0.7); border-radius: 20px; }}
    #logPrintButton:hover {{ background-color: rgba(0, 0, 0, 0.7); }}
    #confirmButton {{ background-color: {theme['success']}; }}
    #infoLabel {{ color: {theme['text_muted']}; font-style: italic; font-size: {s_main_font_size - 2}pt; }}

    /* Modern Calculator Styling */
    #modernCalculator {{
        background-color: {theme['surface']};
        border: 2px solid {theme['border']};
        border-radius: 15px;
    }}
    #modernDisplay {{
        background-color: {theme['background']};
        border: 1px solid {theme['border']};
        border-radius: 6px;
        padding: 8px;
        font-size: {int(14 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: {theme['text']};
    }}
    #numButton {{
        background-color: {theme['hover']};
        border: 1px solid {theme['border']};
        border-radius: 6px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: {theme['text']};
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #numButton:hover {{ background-color: {theme['border']}; }}
    #numButton:pressed {{ background-color: {theme['surface']}; }}
    #clearButton {{
        background-color: #e74c3c;
        border: 1px solid #c0392b;
        border-radius: 6px;
        font-size: {int(13 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #clearButton:hover {{ background-color: #c0392b; }}
    #backButton {{
        background-color: #f39c12;
        border: 1px solid #e67e22;
        border-radius: 6px;
        font-size: {int(13 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #backButton:hover {{ background-color: #e67e22; }}
    #stockButton {{
        background-color: #27ae60;
        border: 1px solid #229954;
        border-radius: 6px;
        font-size: {int(10 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #stockButton:hover {{ background-color: #229954; }}
    #stockButton:disabled {{ background-color: #7f8c8d; border-color: #95a5a6; }}
    #usageButton {{
        background-color: #e74c3c;
        border: 1px solid #c0392b;
        border-radius: 6px;
        font-size: {int(10 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #usageButton:hover {{ background-color: #c0392b; }}
    #usageButton:disabled {{ background-color: #7f8c8d; border-color: #95a5a6; }}
    #commaButton {{
        background-color: {theme['hover']};
        border: 1px solid {theme['border']};
        border-radius: 6px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: {theme['text']};
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #commaButton:hover {{ background-color: {theme['border']}; }}
    #commaButton:pressed {{ background-color: {theme['surface']}; }}
    QComboBox {{
        background-color: {theme['surface']};
        color: {theme['text']};
        border: 1px solid {theme['border']};
        border-radius: {s_padding}px;
        padding: {s_padding}px;
        padding-left: {s_padding + 5}px;
        font-weight: bold;
        font-size: {s_main_font_size}pt;
    }}
    QComboBox::drop-down {{
        border: 0px;
        width: 0px;
    }}
    QComboBox QAbstractItemView {{
        background-color: {theme['surface']};
        border: 1px solid {theme['border']};
        color: {theme['text']};
        selection-background-color: {theme['border']};
    }}
    QLineEdit, QSpinBox, QDoubleSpinBox {{
        background-color: {theme['surface']};
        color: {theme['text']};
        border: 1px solid {theme['border']};
        border-radius: {s_padding}px;
        padding: {s_padding}px;
        font-size: {s_main_font_size}pt;
        font-weight: bold;
    }}
    QLineEdit#currencyInput {{
        text-align: center;
    }}
    QLabel {{ color: {theme['text']}; font-size: {s_main_font_size}pt; font-weight: bold; }}
    QPushButton {{ background-color: {theme['border']}; color: {theme['text']}; font-weight: bold; border-radius: {s_padding}px; padding: {s_btn_padding_v}px {s_btn_padding_h}px; }}
    QPushButton:hover {{ background-color: {theme['hover']}; }}
    QCheckBox {{ color: {theme['text']}; font-size: {s_main_font_size}pt; font-weight: bold; }}
    QCheckBox::indicator {{ width: 15px; height: 15px; border: 1px solid {theme['text_muted']}; border-radius: 4px;}}
    QCheckBox::indicator:checked {{ background-color: {theme['primary']}; border: 1px solid {theme['primary']}; }}
    #filterButton {{ background-color: transparent; border: 1px solid {theme['border']}; border-radius: {s_filter_btn_radius}px; }}
    #filterButton:hover {{ background-color: {theme['hover']}; }}
    QLineEdit#search_input {{ padding-left: 10px; border-radius: {s_control_radius}px; }}
    QLineEdit#currencyInputCapsule {{ border-radius: {s_radius}px; }}
    #themeCombo {{ border-radius: {s_radius}px; min-width: 120px; }}
    #infoButton {{
        background-color: {theme['surface']};
        border: 1px solid {theme['border']};
        border-radius: {s_radius}px;
    }}
    #infoButton:hover {{ background-color: {theme['hover']}; }}
    QTextEdit#notesField {{
        background-color: {theme['surface']};
        color: {theme['text']};
        border: none;
        border-radius: {s_radius}px;
        padding: {s_padding}px;
        font-size: {s_main_font_size}pt;
        font-weight: normal;
    }}
    #dashboardCard, #statCard {{
        background-color: {theme['surface']};
        border-radius: {s_radius}px;
    }}
    #topListScrollArea, #topListScrollArea > QWidget, #topListScrollArea > QWidget > QWidget,
    #lowStockScrollArea, #lowStockScrollArea > QWidget, #lowStockScrollArea > QWidget > QWidget,
    #topListScrollContent, #lowStockScrollContent {{
        background-color: {theme['surface']} !important;
        border: none !important;
    }}
    #statCardTitle {{
        font-size: {s_main_font_size}pt;
        color: {theme['text_muted']};
        font-weight: bold;
    }}
    #statValue {{
        font-size: {int(28 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: {theme['text']};
    }}
    QTextEdit#notesFieldDashboard {{
        background-color: transparent;
        color: {theme['text']};
        border: none;
        padding: 0px;
        font-size: {s_main_font_size}pt;
        font-weight: normal;
    }}
    #historyEntryCard {{ background-color: {theme['hover']}; border-radius: 8px; padding: 10px; }}
    #historyProductTitle {{ font-size: {s_main_font_size+2}pt; font-weight: bold; }}
    #historyDate {{ color: {theme['text_muted']}; font-size: {s_main_font_size-1}pt; }}
    #historyCostLabel {{ font-weight: bold; font-size: {s_main_font_size}pt; }}
    #historyFilamentLabel {{ color: {theme['text']}; font-size: {s_main_font_size-1}pt; }}
    #historyImage {{ background-color: {theme['surface']}; border-radius: 6px; }}
    #notificationPopup {{ background-color: {theme['background']}; border: 1px solid {theme['border']}; border-radius: 5px; }}
    QPushButton#dismissButton {{ font-size: {s_main_font_size - 2}pt; padding: 2px 8px; background-color: {theme['hover']}; }}
    QPushButton#dismissButton:hover {{ background-color: {theme['border']}; }}
    #optionsFrame {{ border: 1px solid {theme['border']}; border-radius: {s_radius}px; padding: 10px; }}
    #lengthInfoPopup {{ background-color: {theme['background']}; border: 1px solid {theme['border']}; border-radius: {s_radius}px; }}
    #lengthBasicInfo {{
        font-size: {int(14 * SCALE_FACTOR)}pt;
        color: {theme['primary']};
        padding: 10px;
        background-color: {theme['surface']};
        border-radius: {s_radius}px;
    }}
    #comparisonText {{
        font-size: {int(14 * SCALE_FACTOR)}pt;
        color: {theme['text']};
        font-weight: bold;
        margin: 10px 0px;
    }}
    #timesText {{
        font-size: {int(20 * SCALE_FACTOR)}pt;
        color: {theme['success']};
        font-weight: bold;
        margin: 10px 0px;
        padding: 5px;
    }}
    """

# --- Konfiguration & Datenmodell ---
def get_data_file() -> Path:
    """Get the filaments.json file path"""
    return get_data_path() / "filaments.json"

def get_settings_file() -> Path:
    """Get the settings.json file path"""
    return get_data_path() / "settings.json"

def get_products_file() -> Path:
    """Get the products.json file path"""
    return get_data_path() / "products.json"

def get_print_history_file() -> Path:
    """Get the print_history.json file path"""
    return get_data_path() / "print_history.json"

# Legacy constants for backward compatibility (will be dynamic)
APP_DIR = get_app_directory()
DATA_DIR = get_data_path()
DATA_FILE = get_data_file()
SETTINGS_FILE = get_settings_file()
PRODUCTS_FILE = get_products_file()
PRINT_HISTORY_FILE = get_print_history_file()
PRODUCTS_DIR = get_products_path()

# Initiale Dateien beim ersten Start kopieren
copy_initial_files()

from models import Filament, Product, Variation, FilamentComponent, UsedComponent, PrintLogEntry, Notification


# --- Daten- und Einstellungsfunktionen ---
def get_available_brands() -> list[str]:
    """
    Lädt die verfügbaren Marken aus der brands.txt Datei.
    """
    brands_file = DATA_DIR / "brands.txt"
    brands: list[str] = []

    if not brands_file.exists():
        # Fallback brands if file doesn't exist
        return ["Generic", "Anycubic", "Bambulab", "Creality", "Elegoo", "Eryone", "eSun", "GEEETECH", "Hatchbox", "JAYO", "Overture", "Polymaker", "Prusa", "Sunlu"]

    try:
        with brands_file.open("r", encoding="utf-8") as f:
            for line in f:
                brand = line.strip()
                if brand:  # Skip empty lines
                    brands.append(brand)
    except (IOError, UnicodeDecodeError) as e:
        logger.warning(f"Could not read brands.txt: {e}")
        # Return fallback brands
        return ["Generic", "Anycubic", "Bambulab", "Creality", "Elegoo", "Eryone", "eSun", "GEEETECH", "Hatchbox", "JAYO", "Overture", "Polymaker", "Prusa", "Sunlu"]

    return brands

def load_data() -> list[Filament]:
    """
    Lädt die Filamentdaten aus der JSON-Datei.
    Gibt eine Liste von Filament-Objekten zurück.
    """
    data_file = get_data_file()
    if not data_file.exists():
        return []
    try:
        with data_file.open("r", encoding="utf-8") as f:
            data = json.load(f)

            # Validierung der JSON-Struktur
            if not isinstance(data, list):
                logger.error("Filament-Daten sind nicht im erwarteten Array-Format")
                return []

            filaments: list[Filament] = []
            for i, item in enumerate(data):
                try:
                    # Validierung der erforderlichen Felder
                    if not isinstance(item, dict):
                        logger.warning(f"Filament-Eintrag {i} ist kein Dictionary, überspringe")
                        continue

                    # Validierung und Setzen von Defaults
                    required_fields = ['brand', 'type', 'color', 'in_stock_grams', 'used_grams']
                    for field in required_fields:
                        if field not in item:
                            logger.warning(f"Filament-Eintrag {i} fehlt erforderliches Feld '{field}', setze Standard")
                            if field in ['in_stock_grams', 'used_grams']:
                                item[field] = 0
                            else:
                                item[field] = ""

                    # Typ-Validierung und Konvertierung
                    if not isinstance(item.get('in_stock_grams'), (int, float)):
                        item['in_stock_grams'] = 0
                    if not isinstance(item.get('used_grams'), (int, float)):
                        item['used_grams'] = 0

                    item.setdefault('spool_weight_grams', item.get('in_stock_grams', 1000))
                    item.setdefault('purchase_price', 0.0)
                    item.setdefault('empty_spool_weight', 0)
                    item.setdefault('id', str(uuid.uuid4()))  # Backup ID falls fehlt
                    item.setdefault('notes', '')  # Default empty notes for existing filaments

                    filaments.append(Filament(**item))
                except (TypeError, ValueError) as e:
                    logger.warning(f"Konnte Filament-Eintrag {i} nicht laden: {e}")
                    continue

            return filaments

    except json.JSONDecodeError as e:
        logger.error(f"JSON-Fehler beim Laden der Filament-Daten: {e}")
        return []
    except (IOError, OSError) as e:
        logger.error(f"Dateifehler beim Laden der Filament-Daten: {e}")
        return []
    except Exception as e:
        logger.error(f"Unerwarteter Fehler beim Laden der Filament-Daten: {e}")
        return []

def save_data(filaments: list[Filament]) -> None:
    """
    Speichert die Liste der Filamente als JSON.
    """
    data_file = get_data_file()
    try:
        logger.info(f"Attempting to save data to: {data_file}")
        logger.info(f"Data file parent directory: {data_file.parent}")
        logger.info(f"App directory: {get_app_directory()}")
        logger.info(f"Data directory: {get_data_path()}")

        # Ensure Data directory exists before saving
        data_file.parent.mkdir(parents=True, exist_ok=True)

        # Backup erstellen falls Datei bereits existiert
        if data_file.exists():
            backup_file = data_file.with_suffix('.json.backup')
            try:
                shutil.copy2(data_file, backup_file)
                logger.info(f"Backup created: {backup_file}")
            except (OSError, PermissionError) as e:
                logger.warning(f"Konnte Backup nicht erstellen: {e}")

        # Verzeichnis sicherstellen
        data_file.parent.mkdir(parents=True, exist_ok=True)
        logger.info(f"Directory created/verified: {data_file.parent}")

        # Daten validieren vor dem Speichern
        if not isinstance(filaments, list):
            raise ValueError("Filaments muss eine Liste sein")

        # JSON-Daten vorbereiten und validieren
        json_data = []
        for i, filament in enumerate(filaments):
            try:
                filament_dict = asdict(filament)
                # Validierung der kritischen Felder
                required_fields = ['id', 'brand', 'type', 'color', 'in_stock_grams', 'used_grams']
                for field in required_fields:
                    if field not in filament_dict:
                        logger.warning(f"Filament {i} fehlt Feld '{field}', setze Standard")
                        if field == 'id':
                            filament_dict[field] = str(uuid.uuid4())
                        elif field in ['in_stock_grams', 'used_grams']:
                            filament_dict[field] = 0
                        else:
                            filament_dict[field] = ""

                json_data.append(filament_dict)
            except Exception as e:
                logger.error(f"Konnte Filament {i} nicht für JSON vorbereiten: {e}")
                continue

        # In temporäre Datei schreiben und dann verschieben (atomic write)
        temp_file = data_file.with_suffix('.json.tmp')
        try:
            with temp_file.open("w", encoding="utf-8") as f:
                json.dump(json_data, f, indent=4, ensure_ascii=False)

            # Atomic move (auf den meisten Systemen)
            if data_file.exists():
                data_file.unlink()
            temp_file.rename(data_file)

        except Exception as e:
            # Cleanup temp file bei Fehler
            if temp_file.exists():
                try:
                    temp_file.unlink()
                except:
                    pass
            raise e

    except (OSError, PermissionError) as e:
        logger.error(f"Dateisystem-Fehler beim Speichern der Filament-Daten: {e}")
        raise e
    except json.JSONEncodeError as e:
        logger.error(f"JSON-Fehler beim Speichern der Filament-Daten: {e}")
        raise e
    except Exception as e:
        logger.error(f"Unerwarteter Fehler beim Speichern der Filament-Daten: {e}")
        raise e

def load_settings() -> dict:
    """
    Lädt die Einstellungen aus der settings.json.
    Gibt ein Dictionary mit allen Einstellungen zurück (inkl. Defaults).
    """
    defaults = {
        "low_stock_threshold": 1500,
        "critical_stock_threshold": 500,
        "track_only_used_in_products": False,
        "currency_symbol": "€",
        "user_notes": "",
        "products_printed": 0,
        "dismissed_notifications": [],
        "theme": "Dark Modern"
    }
    settings_file = get_settings_file()
    if not settings_file.exists():
        return defaults
    try:
        with settings_file.open("r", encoding="utf-8") as f:
            settings = json.load(f)
            defaults.update(settings)
            return defaults
    except (json.JSONDecodeError, IOError):
        return defaults

def save_settings(settings: dict) -> None:
    """
    Speichert die Einstellungen als JSON.
    """
    settings_file = get_settings_file()
    settings_file.parent.mkdir(parents=True, exist_ok=True)
    with settings_file.open("w", encoding="utf-8") as f:
        json.dump(settings, f, indent=4)

def load_products() -> list[Product]:
    """
    Lädt die Produktdaten aus der JSON-Datei.
    Gibt eine Liste von Product-Objekten zurück.
    """
    products_file = get_products_file()
    if not products_file.exists():
        return []
    try:
        with products_file.open("r", encoding="utf-8") as f:
            data = json.load(f)
            products: list[Product] = []
            for item in data:
                variations_data = item.get("variations", [])
                variations = []
                for var_data in variations_data:
                    components_data = var_data.get("components", [])
                    components = [FilamentComponent(**comp) for comp in components_data]
                    variations.append(Variation(name=var_data.get("name", ""), components=components))
                item['variations'] = variations
                products.append(Product(**item))
            return products
    except (json.JSONDecodeError, IOError):
        return []

def save_products(products: list[Product]) -> None:
    """
    Speichert die Liste der Produkte als JSON.
    """
    products_file = get_products_file()
    products_file.parent.mkdir(parents=True, exist_ok=True)
    with products_file.open("w", encoding="utf-8") as f:
        json.dump([asdict(p) for p in products], f, indent=4)

def load_print_history() -> list[PrintLogEntry]:
    """
    Lädt die Druckhistorie aus der JSON-Datei.
    Gibt eine Liste von PrintLogEntry-Objekten zurück.
    """
    print_history_file = get_print_history_file()
    if not print_history_file.exists():
        return []
    try:
        with print_history_file.open("r", encoding="utf-8") as f:
            data = json.load(f)
            history: list[PrintLogEntry] = []
            for item in data:
                components_data = item.get("components", [])
                components = [UsedComponent(**comp) for comp in components_data]
                item['components'] = components
                history.append(PrintLogEntry(**item))
            # Sort by date, newest first
            history.sort(key=lambda x: x.timestamp, reverse=True)
            return history
    except (json.JSONDecodeError, IOError):
        return []

def save_print_history(history: list[PrintLogEntry]) -> None:
    """
    Speichert die Druckhistorie als JSON.
    """
    print_history_file = get_print_history_file()
    print_history_file.parent.mkdir(parents=True, exist_ok=True)
    with print_history_file.open("w", encoding="utf-8") as f:
        json.dump([asdict(h) for h in history], f, indent=4)

def get_log_book_file() -> Path:
    """Get the path to the log book file."""
    return get_data_path() / "log_book.json"

def load_log_book() -> list:
    """Load log book entries from JSON file."""
    log_book_file = get_log_book_file()
    if not log_book_file.exists():
        return []
    try:
        with log_book_file.open("r", encoding="utf-8") as f:
            data = json.load(f)
            return data if isinstance(data, list) else []
    except (json.JSONDecodeError, IOError):
        return []

def save_log_book(log_book: list) -> None:
    """Save log book entries to JSON file."""
    log_book_file = get_log_book_file()
    log_book_file.parent.mkdir(parents=True, exist_ok=True)
    with log_book_file.open("w", encoding="utf-8") as f:
        json.dump(log_book, f, indent=4)

def create_cropped_pixmap(
    image_path: str,
    target_width: int,
    target_height: int,
    radius: int
) -> QPixmap:
    """
    Lädt ein Bild, schneidet es passend zu und gibt ein QPixmap mit abgerundeten Ecken zurück.
    Berücksichtigt HiDPI-Skalierung.
    """
    # Get the screen's device pixel ratio to scale for HiDPI displays
    screen = QApplication.primaryScreen()
    pixel_ratio = screen.devicePixelRatio() if screen else 1.0

    # Calculate the target size in device pixels for a high-resolution pixmap
    target_device_width = int(target_width * pixel_ratio)
    target_device_height = int(target_height * pixel_ratio)
    device_radius = int(radius * pixel_ratio)

    try:
        source_pixmap = QPixmap(str(image_path))
        if source_pixmap.isNull():
            raise IOError("Cannot load image to QPixmap.")
    except Exception:
        return QPixmap()

    original_width = source_pixmap.width()
    original_height = source_pixmap.height()
    target_aspect = target_width / target_height

    # Determine crop box based on logical dimensions
    if (original_width / original_height) > target_aspect:
        # Original is wider than target
        new_height = original_height
        new_width = int(target_aspect * new_height)
        left = (original_width - new_width) / 2
        top = 0
    else:
        # Original is taller than target
        new_width = original_width
        new_height = int(new_width / target_aspect)
        left = 0
        top = (original_height - new_height) / 2

    crop_rect = QRect(int(left), int(top), int(new_width), int(new_height))
    cropped_pixmap = source_pixmap.copy(crop_rect)

    # Explicitly scale the cropped pixmap to the target DEVICE PIXEL size
    scaled_pixmap = cropped_pixmap.scaled(target_device_width, target_device_height,
                                          Qt.AspectRatioMode.IgnoreAspectRatio,
                                          Qt.TransformationMode.SmoothTransformation)

    # Create a final pixmap to apply the rounded corner mask
    final_pixmap = QPixmap(scaled_pixmap.size())
    final_pixmap.fill(Qt.GlobalColor.transparent)

    # Use QPainter to apply the rounded corners
    painter = QPainter(final_pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

    path = QPainterPath()
    path.addRoundedRect(QRectF(final_pixmap.rect()), device_radius, device_radius)
    painter.setClipPath(path)

    # Draw the already-scaled pixmap onto the final canvas
    painter.drawPixmap(0, 0, scaled_pixmap)
    painter.end()

    # Tell the pixmap its pixel density so it displays correctly on HiDPI screens
    final_pixmap.setDevicePixelRatio(pixel_ratio)

    return final_pixmap

def show_custom_message(parent, icon_name: str, icon_color: str, title: str, text: str):
    """Creates and shows a custom QMessageBox without playing the system's notification sound."""
    msg_box = QMessageBox(parent)
    # Use a custom pixmap to avoid triggering system sounds tied to standard icons.
    icon = qta.icon(icon_name, color=icon_color)
    pixmap = icon.pixmap(QSize(48, 48))
    msg_box.setIconPixmap(pixmap)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.exec()

def ask_custom_question(parent, title, text) -> bool:
    """Creates and shows a custom question QMessageBox without sound."""
    msg_box = QMessageBox(parent)
    icon = qta.icon('fa5s.question-circle', color='#3498db') # A neutral blue for questions
    pixmap = icon.pixmap(QSize(48, 48))
    msg_box.setIconPixmap(pixmap)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
    msg_box.setDefaultButton(QMessageBox.StandardButton.No)
    return msg_box.exec() == QMessageBox.StandardButton.Yes

# --- Dialoge ---
class AdjustUsageDialog(QDialog):
    usage_logged = Signal(float)
    stock_added = Signal(float)

    def __init__(self, filament_name, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Adjust: {filament_name}")
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setObjectName("modernCalculator")
        # Let the dialog size itself based on content
        self.setMinimumSize(int(180 * SCALE_FACTOR), int(240 * SCALE_FACTOR))

        self.current_input = ""
        self.filament_name = filament_name  # Store filament name for log book
        self.setup_ui()
        self.installEventFilter(self)

        # Auto-close timer for better UX
        self.auto_close_timer = QTimer()
        self.auto_close_timer.timeout.connect(self.reject)
        self.auto_close_timer.setSingleShot(True)

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # Compact display
        self.display = QLabel("0 g")
        self.display.setObjectName("modernDisplay")
        self.display.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
        self.display.setMinimumHeight(int(35 * SCALE_FACTOR))
        main_layout.addWidget(self.display)

        # Compact numpad with proper spacing
        numpad_widget = QWidget()
        numpad_layout = QGridLayout(numpad_widget)
        numpad_layout.setHorizontalSpacing(6)  # Restore proper horizontal spacing
        numpad_layout.setVerticalSpacing(8)    # Keep vertical spacing
        numpad_layout.setContentsMargins(0, 0, 0, 0)

        # Clean 4x3 calculator layout - standard and simple
        buttons = [
            # Row 0: 1-2-3
            ('1', 0, 0, 'numButton'), ('2', 0, 1, 'numButton'), ('3', 0, 2, 'numButton'),
            # Row 1: 4-5-6
            ('4', 1, 0, 'numButton'), ('5', 1, 1, 'numButton'), ('6', 1, 2, 'numButton'),
            # Row 2: 7-8-9
            ('7', 2, 0, 'numButton'), ('8', 2, 1, 'numButton'), ('9', 2, 2, 'numButton'),
            # Row 3: C-0-, with comma replacing backspace
            ('C', 3, 0, 'clearButton'), ('0', 3, 1, 'numButton'), (',', 3, 2, 'commaButton')
        ]

        for text, row, col, style_class in buttons:
            button = QPushButton(text)
            button.setObjectName(style_class)
            # Make buttons just a tiny bit wider than before
            button.setFixedSize(int(63 * SCALE_FACTOR), int(38 * SCALE_FACTOR))
            button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            button.clicked.connect(partial(self.numpad_button_clicked, text))
            numpad_layout.addWidget(button, row, col)

        main_layout.addWidget(numpad_widget)

        # Text field for log book entry
        self.log_text_input = QLineEdit()
        self.log_text_input.setPlaceholderText("What did you print? (optional)")
        self.log_text_input.setObjectName("logTextInput")
        self.log_text_input.setMinimumHeight(int(25 * SCALE_FACTOR))
        self.log_text_input.setMaxLength(100)  # Reasonable limit
        # Make placeholder text smaller
        self.log_text_input.setStyleSheet(f"""
            QLineEdit {{
                font-size: {int(9 * SCALE_FACTOR)}pt;
            }}
            QLineEdit::placeholder {{
                font-size: {int(8 * SCALE_FACTOR)}pt;
                color: #888;
            }}
        """)
        main_layout.addWidget(self.log_text_input)

        # Compact action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(6)

        self.log_usage_btn = QPushButton("− Log")
        self.log_usage_btn.setObjectName("usageButton")
        self.log_usage_btn.setMinimumHeight(int(30 * SCALE_FACTOR))
        self.log_usage_btn.setMaximumWidth(int(105 * SCALE_FACTOR))
        self.log_usage_btn.clicked.connect(self.log_usage)

        self.add_stock_btn = QPushButton("＋ Add")
        self.add_stock_btn.setObjectName("stockButton")
        self.add_stock_btn.setMinimumHeight(int(30 * SCALE_FACTOR))
        self.add_stock_btn.setMaximumWidth(int(105 * SCALE_FACTOR))
        self.add_stock_btn.clicked.connect(self.add_stock)

        action_layout.addWidget(self.log_usage_btn)
        action_layout.addWidget(self.add_stock_btn)
        main_layout.addLayout(action_layout)

    def eventFilter(self, source, event):
        if event.type() == event.Type.WindowDeactivate:
            self.auto_close_timer.start(100)  # Small delay to prevent accidental closes
        elif event.type() == event.Type.WindowActivate:
            self.auto_close_timer.stop()
        return super().eventFilter(source, event)

    def numpad_button_clicked(self, text):
        if text.isdigit():
            if len(self.current_input) < 8:  # Allow up to 8 characters (including comma)
                self.current_input += text
        elif text == ',':
            # Add comma only if there isn't one already and there are digits
            if ',' not in self.current_input and self.current_input and len(self.current_input) < 8:
                self.current_input += ','
        elif text == 'C':
            self.current_input = ""
        self.update_display()

    def update_display(self):
        display_text = self.current_input if self.current_input else "0"
        # Add thousand separators for better readability (only for whole numbers without comma)
        if display_text != "0" and len(display_text) > 3 and ',' not in display_text:
            try:
                display_text = f"{int(display_text):,}"
            except ValueError:
                pass  # Keep original text if conversion fails
        self.display.setText(f"{display_text} g")

        # Enable/disable buttons based on input
        has_value = bool(self.current_input)
        self.log_usage_btn.setEnabled(has_value)
        self.add_stock_btn.setEnabled(has_value)

    def get_value_from_input(self) -> float:
        if not self.current_input:
            return 0.0
        try:
            # Handle comma as decimal separator for grams (not kilograms)
            if ',' in self.current_input:
                # Convert comma to dot for float parsing - input is already in grams
                value_str = self.current_input.replace(',', '.')
                return float(value_str)  # Keep decimal precision
            else:
                # Whole grams
                return float(self.current_input)
        except ValueError:
            return 0.0

    def add_stock(self):
        value = self.get_value_from_input()
        if value > 0:
            # Get the log text
            log_text = self.log_text_input.text().strip()

            # Emit the value
            self.stock_added.emit(value)

            # Save to log book if there's text
            if log_text:
                self.save_to_log_book_stock(value, log_text)

            self.accept()

    def save_to_log_book_stock(self, grams_added: float, description: str):
        """Save stock addition entry to log book."""
        try:
            from datetime import datetime

            # Load existing log book
            log_book = load_log_book()

            # Create new entry
            entry = {
                "id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "type": "stock_added",
                "grams_used": grams_added,
                "description": description,
                "filament_name": self.filament_name
            }

            # Add to beginning of log book
            log_book.insert(0, entry)

            # Save log book
            save_log_book(log_book)

        except Exception as e:
            logger.warning(f"Failed to save stock addition to log book: {e}")

    def log_usage(self):
        value = self.get_value_from_input()
        if value > 0:
            # Get the log text
            log_text = self.log_text_input.text().strip()

            # Emit both the value and the log text
            self.usage_logged.emit(value)

            # Save to log book if there's text
            if log_text:
                self.save_to_log_book(value, log_text)

            self.accept()

    def save_to_log_book(self, grams_used: float, description: str):
        """Save usage entry to log book."""
        try:
            from datetime import datetime

            # Load existing log book
            log_book = load_log_book()

            # Create new entry
            entry = {
                "id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "type": "usage_logged",
                "grams_used": grams_used,
                "description": description,
                "filament_name": self.filament_name
            }

            # Add to beginning of log book
            log_book.insert(0, entry)

            # Save log book
            save_log_book(log_book)

        except Exception as e:
            logger.warning(f"Failed to save to log book: {e}")

class LogPrintDialog(QDialog):
    def __init__(self, product: Product, parent=None):
        super().__init__(parent)
        self.product = product
        self.setWindowTitle("Log a Print")
        self.setMinimumWidth(int(400 * SCALE_FACTOR))

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(int(15 * SCALE_FACTOR))

        # Title
        title_label = QLabel("Log a Print")
        title_label.setObjectName("titleLabel")

        # Product Info
        product_label = QLabel(f"Product: {self.product.name}")
        product_label.setObjectName("subTitleLabel")

        # Variation Selection
        form_layout = QFormLayout()
        self.variation_combo = QComboBox()
        # Add regular variations first, then Custom at the bottom
        regular_variations = [var for var in self.product.variations if var.name != "Custom"]
        custom_variations = [var for var in self.product.variations if var.name == "Custom"]

        for var in regular_variations:
            self.variation_combo.addItem(var.name)
        for var in custom_variations:
            self.variation_combo.addItem(var.name)
        form_layout.addRow("Which variation?", self.variation_combo)

        # Info Text
        info_label = QLabel("If you press confirm, filament will be reduced from your stock.")
        info_label.setObjectName("infoLabel")
        info_label.setWordWrap(True)

        # Dialog buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        confirm_button = self.button_box.button(QDialogButtonBox.StandardButton.Ok)
        if confirm_button:
            confirm_button.setText("Confirm")
            confirm_button.setObjectName("confirmButton")
        self.button_box.accepted.connect(self.handle_accept)
        self.button_box.rejected.connect(self.reject)

        main_layout.addWidget(title_label)
        main_layout.addWidget(product_label)
        main_layout.addLayout(form_layout)
        main_layout.addWidget(info_label)
        main_layout.addWidget(self.button_box)

    def handle_accept(self):
        selected_variation = self.get_selected_variation()
        if selected_variation and selected_variation.name == "Custom":
            # Show custom variation dialog
            reply = QMessageBox.question(
                self,
                "Custom Variation Selected",
                "You have selected the Custom variation.\n\nThis means you need to manually reduce the filament stock amounts yourself.\n\nDo you want to continue logging this print?",
                QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel,
                QMessageBox.StandardButton.Cancel
            )
            if reply == QMessageBox.StandardButton.Ok:
                self.accept()
        else:
            self.accept()

    def get_selected_variation(self) -> Optional[Variation]:
        selected_name = self.variation_combo.currentText()
        return next((var for var in self.product.variations if var.name == selected_name), None)


class FilamentDialog(QDialog):
    def __init__(self, filament: Optional[Filament] = None, currency_symbol: str = "€", parent=None):
        super().__init__(parent)
        self.is_editing = filament is not None
        self.setWindowTitle("Edit Filament" if self.is_editing else "Add Filament")
        self.setMinimumWidth(int(450 * SCALE_FACTOR))
        self.selected_color = None

        self.brand_input = QComboBox()
        self.brand_input.setEditable(False)  # Make it non-editable for button-like behavior
        self.brand_input.setIconSize(QSize(int(24*SCALE_FACTOR), int(24*SCALE_FACTOR)))

        self.brand_input.installEventFilter(self)

        available_brands = get_available_brands()
        for brand_name in available_brands:
            self.brand_input.addItem(brand_name)

        self.type_input = QLineEdit()
        self.color_name_input = QLineEdit()
        self.price_input = QDoubleSpinBox(); self.price_input.setRange(0, 9999.99); self.price_input.setSuffix(f" {currency_symbol}"); self.price_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        self.price_input.setFocusPolicy(Qt.FocusPolicy.StrongFocus); self.price_input.wheelEvent = lambda event: event.ignore()  # Allow parent scrolling
        self.in_stock_input = QDoubleSpinBox(); self.in_stock_input.setRange(0, 10000.0); self.in_stock_input.setDecimals(1); self.in_stock_input.setSuffix(" g"); self.in_stock_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        self.in_stock_input.setFocusPolicy(Qt.FocusPolicy.StrongFocus); self.in_stock_input.wheelEvent = lambda event: event.ignore()  # Allow parent scrolling
        self.empty_spool_weight_input = QSpinBox(); self.empty_spool_weight_input.setRange(0, 1000); self.empty_spool_weight_input.setSuffix(" g"); self.empty_spool_weight_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        self.empty_spool_weight_input.setFocusPolicy(Qt.FocusPolicy.StrongFocus); self.empty_spool_weight_input.wheelEvent = lambda event: event.ignore()  # Allow parent scrolling
        self.pick_color_button = QPushButton("Pick Color"); self.pick_color_button.clicked.connect(self.open_color_picker)

        color_layout = QHBoxLayout()
        color_layout.addWidget(self.color_name_input)
        color_layout.addWidget(self.pick_color_button)

        form_layout = QFormLayout()
        form_layout.addRow("Brand:", self.brand_input)
        form_layout.addRow("Type:", self.type_input)
        form_layout.addRow("Color:", color_layout)
        form_layout.addRow("Price (per 1 KG):", self.price_input)
        form_layout.addRow("In Stock:", self.in_stock_input)
        form_layout.addRow("Empty Spool Weight:", self.empty_spool_weight_input)

        if self.is_editing and filament:
            self.brand_input.setCurrentText(filament.brand)
            self.type_input.setText(filament.type)
            self.color_name_input.setText(filament.color)
            if filament.hex_color: self.selected_color = QColor(filament.hex_color)
            self.price_input.setValue(filament.purchase_price)
            self.in_stock_input.setValue(filament.in_stock_grams)
            self.empty_spool_weight_input.setValue(filament.empty_spool_weight)
        else:
            self.in_stock_input.setValue(1000)

        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

        main_layout = QVBoxLayout(self)
        main_layout.addLayout(form_layout)
        main_layout.addWidget(self.button_box)

    def eventFilter(self, source, event):
        if source is self.brand_input and event.type() == QEvent.Type.MouseButtonPress:
            if not self.brand_input.view().isVisible():
                self.brand_input.showPopup()
                return True
        return super().eventFilter(source, event)

    def open_color_picker(self):
        color = QColorDialog.getColor()
        if color.isValid(): self.selected_color = color

    def get_data(self) -> dict:
        hex_color_val = self.selected_color.name() if self.selected_color else "#808080"  # Default gray color
        data = {"brand": self.brand_input.currentText(),
                "type": self.type_input.text(),
                "color": self.color_name_input.text(),
                "hex_color": hex_color_val,
                "purchase_price": self.price_input.value(),
                "in_stock_grams": float(self.in_stock_input.value()),
                "empty_spool_weight": self.empty_spool_weight_input.value()}
        if not self.is_editing:
            data["spool_weight_grams"] = int(self.in_stock_input.value())
            data["used_grams"] = 0.0
        return data

class LowStockSettingsDialog(QDialog):
    def __init__(self, settings: dict, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Low Stock Alert Settings")
        self.setMinimumWidth(int(400 * SCALE_FACTOR))

        self.low_input = QSpinBox()
        self.low_input.setRange(0, 10000)
        self.low_input.setValue(settings.get("low_stock_threshold", 1500))
        self.low_input.setSuffix(" g")
        self.low_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)

        self.critical_input = QSpinBox()
        self.critical_input.setRange(0, 10000)
        self.critical_input.setValue(settings.get("critical_stock_threshold", 500))
        self.critical_input.setSuffix(" g")
        self.critical_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)

        self.track_used_checkbox = QCheckBox("Only show filament used in products")
        self.track_used_checkbox.setChecked(settings.get("track_only_used_in_products", False))

        form_layout = QFormLayout()
        form_layout.addRow("Yellow warning below:", self.low_input)
        form_layout.addRow("Red warning below:", self.critical_input)
        form_layout.addRow(self.track_used_checkbox)

        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

        main_layout = QVBoxLayout(self)
        main_layout.addLayout(form_layout)
        main_layout.addWidget(self.button_box)

    def get_values(self):
        return {
            "low_stock_threshold": self.low_input.value(),
            "critical_stock_threshold": self.critical_input.value(),
            "track_only_used_in_products": self.track_used_checkbox.isChecked()
        }

class ProductDialog(QDialog):
    def __init__(self, filaments: list[Filament], product: Optional[Product] = None, all_products: list[Product] = None, parent=None):
        super().__init__(parent)
        self.filaments = filaments
        self.all_products = all_products or []
        self.product = product or Product()
        self.is_editing = product is not None
        self.setWindowTitle("Edit Product" if self.is_editing else "Add Product")
        self.setMinimumWidth(int(500 * SCALE_FACTOR))
        self.setMaximumHeight(int(700 * SCALE_FACTOR))

        main_layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        self.name_input = QLineEdit(self.product.name)
        form_layout.addRow("Product Name:", self.name_input)

        image_layout = QHBoxLayout()
        self.image_select_button = QPushButton("Select Image")
        self.image_select_button.clicked.connect(self.select_image)
        self.image_label = QLabel(self.product.image_name or "No image selected")
        image_layout.addWidget(self.image_select_button)
        image_layout.addWidget(self.image_label, 1)
        form_layout.addRow("Image:", image_layout)

        # Create scroll area for variations
        self.variations_scroll = QScrollArea()
        self.variations_scroll.setWidgetResizable(True)
        self.variations_scroll.setMinimumHeight(int(200 * SCALE_FACTOR))
        self.variations_scroll.setMaximumHeight(int(400 * SCALE_FACTOR))
        # Make sure it uses the theme background color
        self.variations_scroll.setFrameShape(QFrame.Shape.NoFrame)
        self.variations_scroll.setObjectName("variationsScrollArea")

        self.variations_widget = QWidget()
        self.variations_widget.setObjectName("variationsWidget")
        self.variations_layout = QVBoxLayout(self.variations_widget)
        self.variations_layout.setContentsMargins(0,0,0,0)
        self.variations_layout.addStretch()  # Add stretch to push variations to top

        self.variations_scroll.setWidget(self.variations_widget)
        # Apply consistent scrollbar style (10px for dialogs) and background
        main_window = self.parent()
        while main_window and not isinstance(main_window, MainWindow):
            main_window = main_window.parent()
        if main_window:
            scrollbar_style = main_window.get_scrollbar_style(10)
            background_style = "QScrollArea#variationsScrollArea { background-color: transparent; } QWidget#variationsWidget { background-color: transparent; }"
            self.variations_scroll.setStyleSheet(scrollbar_style + background_style)
        form_layout.addRow(self.variations_scroll)

        # Variation buttons layout
        variation_buttons_layout = QHBoxLayout()
        self.copy_variation_btn = QPushButton("Copy Variation")
        self.copy_variation_btn.clicked.connect(self.copy_variation)
        add_variation_btn = QPushButton("Add Variation")
        add_variation_btn.clicked.connect(self.add_variation)

        variation_buttons_layout.addWidget(self.copy_variation_btn)
        variation_buttons_layout.addWidget(add_variation_btn)
        form_layout.addRow(variation_buttons_layout)

        # Only add non-Custom variations to the UI
        for variation in self.product.variations:
            if variation.name != "Custom":
                self.add_variation(variation)

        # Create button box with custom layout for copy button
        button_layout = QHBoxLayout()

        # Copy Product button (only for new products)
        if not self.is_editing and self.all_products:
            self.copy_button = QPushButton("Copy Product")
            self.copy_button.clicked.connect(self.show_copy_menu)
            button_layout.addWidget(self.copy_button)

        button_layout.addStretch()

        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        button_layout.addWidget(self.button_box)

        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)

    def show_copy_menu(self):
        if not self.all_products:
            return

        menu = QMenu(self)
        for product in self.all_products:
            action = menu.addAction(product.name)
            action.triggered.connect(lambda checked, p=product: self.copy_from_product(p))

        # Show menu at button position
        button_pos = self.copy_button.mapToGlobal(self.copy_button.rect().bottomLeft())
        menu.exec(button_pos)

    def copy_from_product(self, source_product: Product):
        # Copy name (with "Copy of" prefix)
        self.name_input.setText(f"Copy of {source_product.name}")

        # Copy image
        self.product.image_name = source_product.image_name
        self.image_label.setText(source_product.image_name or "No image selected")

        # Clear existing variations
        for i in reversed(range(self.variations_layout.count())):
            item = self.variations_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
                    widget.deleteLater()

        # Copy variations (excluding Custom variation)
        for variation in source_product.variations:
            if variation.name != "Custom":
                self.add_variation(copy.deepcopy(variation))

    def copy_variation(self):
        # Get all current variations (excluding Custom)
        current_variations = []
        for i in range(self.variations_layout.count()):
            item = self.variations_layout.itemAt(i)
            if item:
                var_widget = item.widget()
                if isinstance(var_widget, VariationWidget):
                    variation_data = var_widget.get_data()
                    if variation_data.name != "Custom":
                        current_variations.append(variation_data)

        if not current_variations:
            show_custom_message(self, 'fa5s.info-circle', '#5bc0de', "No Variations", "No variations available to copy. Please add a variation first.")
            return

        # Show dropdown menu to select variation to copy
        menu = QMenu(self)
        for variation in current_variations:
            action = menu.addAction(f"Copy: {variation.name}")
            action.triggered.connect(lambda checked, v=variation: self.perform_copy_variation(v))

        # Show menu at the copy button position
        button_pos = self.copy_variation_btn.mapToGlobal(self.copy_variation_btn.rect().bottomLeft())
        menu.exec(button_pos)

    def perform_copy_variation(self, source_variation: Variation):
        # Create a deep copy of the variation
        copied_variation = copy.deepcopy(source_variation)
        copied_variation.name = f"Copy of {source_variation.name}"

        # Add the copied variation
        self.add_variation(copied_variation)

    def add_variation(self, variation: Optional[Variation] = None):
        # Remove the stretch
        stretch_item = self.variations_layout.takeAt(self.variations_layout.count() - 1)

        # Add the variation widget
        var_widget = VariationWidget(self.filaments, variation)
        var_widget.remove_requested.connect(lambda: self.remove_widget(var_widget))
        self.variations_layout.addWidget(var_widget)

        # Add the stretch back to keep variations at the top
        self.variations_layout.addItem(stretch_item)

    def remove_widget(self, widget):
        self.variations_layout.removeWidget(widget)
        widget.deleteLater()
        # The stretch will remain at the bottom automatically

    def select_image(self):
        filepath, _ = QFileDialog.getOpenFileName(self, "Select Product Image", "", "Images (*.png *.jpg *.jpeg)")
        if filepath:
            filename = os.path.basename(filepath)
            target_path = os.path.join(PRODUCTS_DIR, filename)
            try:
                shutil.copy(filepath, target_path)
                self.product.image_name = filename
                self.image_label.setText(filename)
            except Exception as e:
                show_custom_message(self, 'fa5s.exclamation-triangle', '#d9534f', "Error", f"Could not copy image: {e}")

    def get_data(self) -> Product:
        self.product.name = self.name_input.text()

        self.product.variations.clear()
        for i in range(self.variations_layout.count()):
            item = self.variations_layout.itemAt(i)
            if item:
                var_widget = item.widget()
                if isinstance(var_widget, VariationWidget):
                     self.product.variations.append(var_widget.get_data())

        # Always add the "Custom" variation (hidden from user editing)
        # Check if it already exists to prevent duplication
        has_custom = any(var.name == "Custom" for var in self.product.variations)
        if not has_custom:
            custom_variation = Variation(name="Custom", components=[])
            self.product.variations.append(custom_variation)

        return self.product

class VariationWidget(QFrame):
    remove_requested = Signal()
    def __init__(self, filaments: list[Filament], variation: Optional[Variation] = None, parent=None):
        super().__init__(parent)
        self.filaments = filaments
        self.variation = variation or Variation(name="", components=[])

        self.setObjectName("variationFrame")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        # Remove any border styling
        self.setStyleSheet("QFrame#variationFrame { border: none; }")
        main_layout = QVBoxLayout(self)

        header_layout = QHBoxLayout()
        self.name_input = QLineEdit(self.variation.name)
        self.name_input.setPlaceholderText("Variation Name (e.g., 'Blue/Black')")
        remove_btn = QPushButton("Remove Variation")
        remove_btn.clicked.connect(self.remove_requested.emit)
        header_layout.addWidget(self.name_input, 1)
        header_layout.addWidget(remove_btn)
        main_layout.addLayout(header_layout)

        self.components_layout = QVBoxLayout()
        main_layout.addLayout(self.components_layout)

        add_component_btn = QPushButton("Add Filament Component")
        add_component_btn.clicked.connect(self.add_component)
        main_layout.addWidget(add_component_btn)

        for component in self.variation.components:
            self.add_component(component)

    def add_component(self, component: Optional[FilamentComponent] = None):
        comp_widget = FilamentComponentWidget(self.filaments, component)
        comp_widget.remove_requested.connect(lambda: self.remove_widget(comp_widget))
        self.components_layout.addWidget(comp_widget)

    def remove_widget(self, widget):
        self.components_layout.removeWidget(widget)
        widget.deleteLater()

    def get_data(self) -> Variation:
        self.variation.name = self.name_input.text()
        self.variation.components.clear()
        for i in range(self.components_layout.count()):
            item = self.components_layout.itemAt(i)
            if item:
                comp_widget = item.widget()
                if isinstance(comp_widget, FilamentComponentWidget):
                    self.variation.components.append(comp_widget.get_data())
        return self.variation

class FilamentSelectionDialog(QDialog):
    def __init__(self, filaments: list[Filament], current_filament_id: str = "", parent=None):
        super().__init__(parent)
        self.filaments = filaments
        self.selected_filament_id = current_filament_id
        self.setWindowTitle("Select Filament")
        self.setMinimumSize(int(400 * SCALE_FACTOR), int(300 * SCALE_FACTOR))

        layout = QVBoxLayout(self)

        # Search bar
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search filaments...")
        self.search_input.textChanged.connect(self.filter_filaments)
        layout.addWidget(self.search_input)

        # Filament list
        self.filament_list = QListWidget()
        self.filament_list.itemDoubleClicked.connect(self.on_filament_selected)
        # Ensure scroll bars are always visible when needed
        self.filament_list.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.filament_list.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        # Apply consistent scrollbar style (8px for small popups)
        self.filament_list.setStyleSheet(self.get_scrollbar_style_for_listwidget(8))
        layout.addWidget(self.filament_list)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        select_btn = QPushButton("Select")
        select_btn.clicked.connect(self.on_filament_selected)
        button_layout.addWidget(select_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        # Populate the list
        self.populate_filament_list()

        # Select current filament if any
        if current_filament_id:
            self.select_current_filament(current_filament_id)

    def get_scrollbar_style_for_listwidget(self, width: int) -> str:
        """Generate consistent capsule-style scrollbar CSS for QListWidget."""
        handle_radius = width // 2
        margin = 2
        return f"""
            QListWidget {{
                border: none;
                background: transparent;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: {width}px;
                margin: 0px;
                border: none;
            }}
            QScrollBar::handle:vertical {{
                background: rgba(255, 255, 255, 0.3);
                border-radius: {handle_radius}px;
                min-height: 20px;
                margin: {margin}px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: rgba(255, 255, 255, 0.5);
            }}
            QScrollBar::handle:vertical:pressed {{
                background: rgba(255, 255, 255, 0.7);
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
                width: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: transparent;
            }}
        """

    def populate_filament_list(self, search_text: str = ""):
        self.filament_list.clear()
        search_text = search_text.lower()

        for filament in self.filaments:
            filament_text = f"{filament.brand} {filament.type} - {filament.color}"
            if not search_text or search_text in filament_text.lower():
                item = QListWidgetItem(filament_text)
                item.setData(Qt.ItemDataRole.UserRole, filament.id)
                self.filament_list.addItem(item)

    def filter_filaments(self, search_text: str):
        self.populate_filament_list(search_text)

    def select_current_filament(self, filament_id: str):
        for i in range(self.filament_list.count()):
            item = self.filament_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == filament_id:
                self.filament_list.setCurrentItem(item)
                break

    def on_filament_selected(self):
        current_item = self.filament_list.currentItem()
        if current_item:
            self.selected_filament_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.accept()

    def get_selected_filament_id(self):
        return self.selected_filament_id

class FilamentComponentWidget(QWidget):
    remove_requested = Signal()
    def __init__(self, filaments: list[Filament], component: Optional[FilamentComponent] = None, parent=None):
        super().__init__(parent)
        self.filaments = filaments
        self.component = component or FilamentComponent(filament_id="", grams_used=0)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(0,0,0,0)

        # Simple button to show filament selection dialog
        self.filament_button = QPushButton()
        self.filament_button.clicked.connect(self.show_filament_selection)
        self.update_filament_button_text()

        self.grams_input = QDoubleSpinBox()
        self.grams_input.setRange(0.1, 10000.0)
        self.grams_input.setDecimals(1)
        self.grams_input.setSuffix(" g")
        self.grams_input.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        self.grams_input.setFocusPolicy(Qt.FocusPolicy.StrongFocus)  # Only focus on click/tab
        # Custom wheel event that ignores the spinbox but allows parent scrolling
        def custom_wheel_event(event):
            # Ignore the wheel event for this widget but let parent handle it
            event.ignore()
        self.grams_input.wheelEvent = custom_wheel_event

        remove_btn = QPushButton("Remove")
        remove_btn.clicked.connect(self.remove_requested.emit)

        layout.addWidget(self.filament_button, 2)
        layout.addWidget(self.grams_input, 1)
        layout.addWidget(remove_btn)

        # Set initial values
        if self.component.filament_id:
            self.grams_input.setValue(self.component.grams_used)

    def update_filament_button_text(self):
        """Update the button text to show the selected filament."""
        if self.component.filament_id:
            # Find the filament by ID
            filament = next((f for f in self.filaments if f.id == self.component.filament_id), None)
            if filament:
                self.filament_button.setText(f"{filament.brand} {filament.type} - {filament.color}")
            else:
                self.filament_button.setText("Select Filament (Invalid)")
        else:
            self.filament_button.setText("Select Filament")

    def show_filament_selection(self):
        """Show the filament selection dialog."""
        dialog = FilamentSelectionDialog(self.filaments, self.component.filament_id, self)
        if dialog.exec():
            selected_id = dialog.get_selected_filament_id()
            if selected_id:
                self.component.filament_id = selected_id
                self.update_filament_button_text()

    def get_data(self) -> FilamentComponent:
        grams_used = float(self.grams_input.value())
        return FilamentComponent(filament_id=self.component.filament_id, grams_used=grams_used)

# --- Zeilen-Widgets ---
class FlashBubbleWidget(QWidget):
    finished_flashing = Signal()

    def __init__(self, text: str, background_color: QColor, anchor_label: QLabel, parent=None):
        super().__init__(parent)
        self.anchor_label = anchor_label
        self.text_to_draw = text
        self.flash_color = background_color
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setWindowFlags(Qt.WindowType.Widget)

        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.finished.connect(self.finished_flashing.emit)
        self.animation.finished.connect(self.close)

        self._timer = QTimer(self)
        self._timer.timeout.connect(self.update_geometry)
        self._timer.setInterval(30)

        self.H_PADDING = int(8 * SCALE_FACTOR)
        self.V_PADDING = int(2 * SCALE_FACTOR)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(self.flash_color)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), self.height() / 2, self.height() / 2)

        font = QFont("Segoe UI", int(10 * SCALE_FACTOR), QFont.Weight.Bold)
        painter.setFont(font)
        painter.setPen(Qt.GlobalColor.white)
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, self.text_to_draw)

    def flash(self, duration_ms: int, fade_out_duration_ms: int):
        self.update_geometry()
        self._timer.start()

        solid_duration = max(0, duration_ms - fade_out_duration_ms)
        self.animation.setDuration(fade_out_duration_ms)
        self.animation.setStartValue(1.0)
        self.animation.setEndValue(0.0)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutCubic)

        self.show()
        self.raise_()
        QTimer.singleShot(solid_duration, self.animation.start)

    def update_geometry(self):
        if not self.anchor_label or not self.anchor_label.isVisibleTo(self.parentWidget()):
            self.hide()
            return

        parent_widget = self.parentWidget()
        if not parent_widget:
            return

        text_rect = self.anchor_label.fontMetrics().boundingRect(self.anchor_label.rect(), self.anchor_label.alignment(), self.anchor_label.text())
        bubble_rect = text_rect.adjusted(-self.H_PADDING, -self.V_PADDING, self.H_PADDING, self.V_PADDING)

        new_pos = self.anchor_label.mapTo(parent_widget, bubble_rect.topLeft())

        if self.pos() != new_pos or self.size() != bubble_rect.size():
            self.setGeometry(QRect(new_pos, bubble_rect.size()))

        self.raise_()

    def closeEvent(self, event):
        self._timer.stop()
        super().closeEvent(event)

class HighlightableLabel(QLabel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def flash(self, change_text: str, color_hex: str, duration_ms: int = 1500, fade_duration_ms: int = 500):
        bubble_color = QColor(color_hex)

        viewport = None
        parent = self.parentWidget()
        while parent:
            if isinstance(parent, QScrollArea):
                viewport = parent.viewport()
                break
            parent = parent.parentWidget()
        if not viewport:
            viewport = self.window()

        bubble = FlashBubbleWidget(change_text, bubble_color, self, parent=viewport)
        bubble.finished_flashing.connect(self._make_visible_again)

        self.setWindowOpacity(0)
        bubble.flash(duration_ms, fade_duration_ms)

    def _make_visible_again(self):
        self.setWindowOpacity(1)

class FilamentRowWidget(QWidget):
    clicked = Signal(str); adjust_clicked = Signal(str); notes_clicked = Signal(str)

    def __init__(self, filament: Filament, parent=None):
        super().__init__(parent); self.filament = filament; self.is_selected = False
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True); self.setObjectName("FilamentRow")
        s_margin = int(BASE_MARGIN * SCALE_FACTOR); s_spacing = int(BASE_SPACING * SCALE_FACTOR)
        s_icon_size = int(BASE_ICON_SIZE * SCALE_FACTOR); s_circle_size = int(BASE_CIRCLE_SIZE * SCALE_FACTOR)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(s_margin, int(10 * SCALE_FACTOR), s_margin, int(10 * SCALE_FACTOR)); layout.setSpacing(s_spacing)
        # Store widget references for update_data method
        self.color_circle = QLabel(); self.color_circle.setObjectName("colorCircle"); self.color_circle.setFixedSize(s_circle_size, s_circle_size)
        self.brand_label = QLabel(); self.brand_label.setObjectName("brandLabel")
        self.type_label = QLabel(); self.type_label.setObjectName("typeLabel")
        self.color_text_label = QLabel()
        self.color_text_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.remaining_label = HighlightableLabel("")
        self.remaining_label.setObjectName("inStockLabel")
        self.remaining_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.used_label = HighlightableLabel("")
        self.used_label.setObjectName("usedLabel")
        self.remaining_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        # Initialize with filament data
        self._update_widget_content(filament)
        self.adjust_button = self.create_row_action_button('calculator', "adjustButton")
        self.adjust_button.clicked.connect(lambda: self.adjust_clicked.emit(self.filament.id))
        self.notes_button = self.create_row_action_button('sticky-note', "notesButton")
        self.notes_button.clicked.connect(lambda: self.notes_clicked.emit(self.filament.id))
        layout.addWidget(self.color_circle, 0); layout.addWidget(self.brand_label, 1); layout.addWidget(self.type_label, 1)
        layout.addSpacing(int(40 * SCALE_FACTOR))  # Extra space between Type and Color
        # Color column for color text
        layout.addWidget(self.color_text_label, 1, Qt.AlignmentFlag.AlignVCenter)
        layout.addSpacing(int(60 * SCALE_FACTOR))  # Gap between Color and Used

        # Group Used, In Stock, Actions in a sub-layout for fine spacing
        right_group = QWidget()
        right_layout = QHBoxLayout(right_group)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(4)
        right_layout.addWidget(self.used_label, 1)
        right_layout.addWidget(self.remaining_label, 1)
        # Actions column to align with header
        actions_widget = QWidget(); actions_widget.setMinimumWidth(int(80 * SCALE_FACTOR))
        actions_layout = QHBoxLayout(actions_widget); actions_layout.setContentsMargins(int(12 * SCALE_FACTOR), 0, 0, 0)
        actions_layout.setSpacing(int(2 * SCALE_FACTOR))
        actions_layout.addWidget(self.adjust_button, 0, Qt.AlignmentFlag.AlignLeft)
        actions_layout.addWidget(self.notes_button, 0, Qt.AlignmentFlag.AlignLeft)
        right_layout.addWidget(actions_widget, 0)
        layout.addWidget(right_group, 3)
    def create_row_action_button(self, icon_name, obj_name):
        s_icon_size = int((BASE_ICON_SIZE - 4) * SCALE_FACTOR); s_btn_size = int(BASE_ROW_ACTION_BTN_SIZE * SCALE_FACTOR)
        button = QPushButton(); button.setObjectName(obj_name)
        # Use QtAwesome icons with proper color for row action buttons
        button.setIcon(qta.icon(f'fa5s.{icon_name}', color='#B0B0B0'))
        button.setIconSize(QSize(s_icon_size, s_icon_size)); button.setFixedSize(s_btn_size, s_btn_size)
        return button
    def mousePressEvent(self, event):
        if not self.adjust_button.underMouse() and not self.notes_button.underMouse():
            self.clicked.emit(self.filament.id); super().mousePressEvent(event)
    def set_selected(self, selected):
        self.is_selected = selected
        self.setProperty("selected", "true" if selected else "false")
        style = self.style()
        if style:
            style.polish(self)

    def _update_widget_content(self, filament: Filament):
        """Helper method to update widget content with filament data"""

        # Update text labels
        self.brand_label.setText(f"{filament.brand}")
        self.type_label.setText(f"{filament.type}")
        self.color_text_label.setText(filament.color)
        self.remaining_label.setText(f"{filament.in_stock_grams:g}g")
        self.used_label.setText(f"{filament.used_grams:g}g")

        # Update color preview with rounded rectangle (like warning notifications)
        if filament.hex_color:
            circle_size = int(BASE_CIRCLE_SIZE * SCALE_FACTOR)
            self.color_circle.setStyleSheet(f"""
                QLabel {{
                    background-color: {filament.hex_color};
                    border-radius: 4px;
                    border: none;
                    min-width: {circle_size}px;
                    max-width: {circle_size}px;
                    min-height: {circle_size}px;
                    max-height: {circle_size}px;
                }}
            """)
            self.color_circle.setText("")  # Clear any content
            self.color_circle.show()
        else:
            self.color_circle.hide()



    def update_data(self, filament: Filament):
        """Update this widget with new filament data without recreating it"""
        old_id = self.filament.id
        self.filament = filament
        self._update_widget_content(filament)

        # Update signal connection if ID changed
        if old_id != filament.id:
            self.adjust_button.clicked.disconnect()
            self.adjust_button.clicked.connect(lambda: self.adjust_clicked.emit(self.filament.id))

class TopFilamentWidget(QWidget):
    def __init__(self, rank, filament: Filament, parent=None):
        super().__init__(parent); self.setObjectName("topFilamentWidget")
        layout = QHBoxLayout(self); layout.setContentsMargins(0, 0, 0, 0); layout.setSpacing(int(10 * SCALE_FACTOR))
        rank_label = QLabel(f"{rank}."); rank_label.setProperty("class", "topRankLabel")
        text_label = QLabel(f"{filament.brand} {filament.type} - {filament.color}"); usage_label = QLabel(f"{filament.used_grams:g}g")
        usage_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(rank_label); layout.addWidget(text_label, 1); layout.addWidget(usage_label)

class WarningFilamentWidget(QWidget):
    def __init__(self, filament: Filament, warning_color: str, parent=None):
        super().__init__(parent); self.setObjectName("warningFilamentWidget")
        s_circle_size = int(BASE_CIRCLE_SIZE * SCALE_FACTOR)
        layout = QHBoxLayout(self); layout.setContentsMargins(0, 0, 0, 0); layout.setSpacing(int(10 * SCALE_FACTOR))
        warning_circle = QLabel(); warning_circle.setObjectName("warningCircle")
        warning_circle.setFixedSize(s_circle_size, s_circle_size)
        warning_circle.setStyleSheet(f"background-color: {warning_color};")
        text_label = QLabel(f"{filament.brand} {filament.type} - {filament.color}")
        remaining_label = QLabel(f"{filament.in_stock_grams:g}g")
        remaining_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(warning_circle); layout.addWidget(text_label, 1); layout.addWidget(remaining_label)

class ProductTileWidget(QWidget):
    clicked = Signal(str)
    log_print_requested = Signal(str)
    product_reordered = Signal(str, str)  # source_product_id, target_product_id

    def __init__(self, product: Product, tile_size: tuple[int, int] = None, parent=None):
        super().__init__(parent)
        self.product = product
        self.is_selected = False
        self.warning_message = None

        # Use provided tile size or default
        if tile_size:
            tile_width, tile_height = tile_size
        else:
            tile_width, tile_height = int(135 * SCALE_FACTOR), int(200 * SCALE_FACTOR)

        self.setFixedSize(tile_width, tile_height)
        self.setObjectName("productTile")
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True)

        # Enable drag and drop - simplified version
        self.setAcceptDrops(True)
        self.drag_start_position = None
        self.is_dragging = False
        self.drag_threshold = 10  # Minimum pixels to start drag

        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(0)

        self.image_label = QLabel()
        self.image_label.setObjectName("productImage")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Calculate image size based on tile size
        image_width = tile_width - 4
        image_height = int(image_width * 4 / 3)
        self.image_label.setFixedSize(image_width, image_height)

        image_path = os.path.join(PRODUCTS_DIR, self.product.image_name)
        if self.product.image_name and os.path.exists(image_path):
            pixmap = create_cropped_pixmap(image_path, image_width, image_height, radius=6)
            self.image_label.setPixmap(pixmap)
        else:
            placeholder_icon = qta.icon('fa5s.box-open', color='#888')
            source_pixmap = placeholder_icon.pixmap(QSize(256, 256))
            s_icon_size = int(60 * SCALE_FACTOR)
            screen = QApplication.primaryScreen()
            pixel_ratio = screen.devicePixelRatio() if screen else 1.0
            target_device_size = int(s_icon_size * pixel_ratio)
            placeholder_pixmap = source_pixmap.scaled(QSize(target_device_size, target_device_size),
                                                      Qt.AspectRatioMode.KeepAspectRatio,
                                                      Qt.TransformationMode.SmoothTransformation)
            placeholder_pixmap.setDevicePixelRatio(pixel_ratio)
            self.image_label.setPixmap(placeholder_pixmap)

        self.title_label = QLabel(self.product.name)
        self.title_label.setObjectName("productTitle")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(self.image_label)
        layout.addWidget(self.title_label)

        s_btn_size = int(40 * SCALE_FACTOR)
        s_icon_size = int(20 * SCALE_FACTOR)
        self.log_button = QPushButton(self.image_label)
        self.log_button.setIcon(load_svg_icon('printer-check', 'white'))
        self.log_button.setIconSize(QSize(s_icon_size, s_icon_size))
        self.log_button.setFixedSize(s_btn_size, s_btn_size)
        self.log_button.setObjectName("logPrintButton")
        # Set rounded rectangle button styling with yellow-orange color like edit button
        self.log_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #d39e00;
                border-radius: {s_radius}px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #e6a800;
            }}
            QPushButton:pressed {{
                background-color: #c28900;
            }}
        """)
        self.log_button.clicked.connect(lambda: self.log_print_requested.emit(self.product.id))

        margin_x = 5
        margin_y = 10
        self.log_button.move(image_width - s_btn_size - margin_x, image_height - s_btn_size - margin_y)

        s_warn_btn_size = int(40 * SCALE_FACTOR)
        s_warn_icon_size = int(20 * SCALE_FACTOR)
        self.warning_button = QPushButton(self.image_label)
        self.warning_button.setIcon(qta.icon('fa5s.exclamation-triangle', color='#ff6b35'))
        self.warning_button.setIconSize(QSize(s_warn_icon_size, s_warn_icon_size))
        self.warning_button.setFixedSize(s_warn_btn_size, s_warn_btn_size)
        self.warning_button.setObjectName("transparentButton")
        self.warning_button.setToolTip("Click to see stock warning")
        self.warning_button.clicked.connect(self.show_warning_details)

        warn_margin = 5
        self.warning_button.move(warn_margin, warn_margin)
        self.warning_button.hide()

    def show_warning(self, message: str):
        self.warning_message = message
        self.warning_button.show()

    def show_warning_details(self):
        if self.warning_message:
            show_custom_message(self, 'fa5s.exclamation-triangle', '#f0ad4e', "Product Warning", self.warning_message)

    def mousePressEvent(self, event):
        if not self.log_button.underMouse() and not self.warning_button.underMouse():
            if event.button() == Qt.MouseButton.LeftButton:
                self.drag_start_position = event.pos()
                self.clicked.emit(self.product.id)
            elif event.button() == Qt.MouseButton.RightButton:
                # Show context menu for reordering
                self.show_context_menu(event.globalPos())
            super().mousePressEvent(event)

    def set_selected(self, selected: bool):
        self.is_selected = selected
        self.setProperty("selected", "true" if selected else "false")
        style = self.style()
        if style:
            style.polish(self)

    def mouseMoveEvent(self, event):
        try:
            if not (event.buttons() & Qt.MouseButton.LeftButton):
                return
            if not self.drag_start_position:
                return

            # Use simple distance check
            distance = (event.pos() - self.drag_start_position).manhattanLength()
            if distance < self.drag_threshold:
                return

            # Don't start drag if over buttons
            if self.log_button.underMouse() or self.warning_button.underMouse():
                return

            self.start_drag()
        except Exception as e:
            logger.warning(f"Mouse move event error: {e}")

    def start_drag(self):
        try:
            self.is_dragging = True
            drag = QDrag(self)
            mime_data = QMimeData()
            mime_data.setText(self.product.id)
            drag.setMimeData(mime_data)

            # Skip pixmap creation to avoid crashes - just use basic drag
            # Start the drag operation
            drag.exec(Qt.DropAction.MoveAction)

        except Exception as e:
            logger.error(f"Drag operation failed: {e}")
        finally:
            self.is_dragging = False

    def dragEnterEvent(self, event):
        try:
            if event.mimeData().hasText() and not self.is_dragging:
                event.acceptProposedAction()
                self.setProperty("dragHover", "true")
                if self.style():
                    self.style().polish(self)
        except Exception as e:
            logger.warning(f"Drag enter event error: {e}")

    def dragLeaveEvent(self, event):
        try:
            self.setProperty("dragHover", "false")
            if self.style():
                self.style().polish(self)
        except Exception as e:
            logger.warning(f"Drag leave event error: {e}")

    def dropEvent(self, event):
        try:
            self.setProperty("dragHover", "false")
            if self.style():
                self.style().polish(self)

            if event.mimeData().hasText():
                source_product_id = event.mimeData().text()
                target_product_id = self.product.id
                if source_product_id != target_product_id:
                    self.product_reordered.emit(source_product_id, target_product_id)
                event.acceptProposedAction()
        except Exception as e:
            logger.error(f"Drop event error: {e}")

    def show_context_menu(self, global_pos):
        """Show context menu for product reordering"""
        try:
            menu = QMenu(self)

            # Add reordering options
            move_left_action = menu.addAction("Move Left")
            move_right_action = menu.addAction("Move Right")
            menu.addSeparator()
            move_to_start_action = menu.addAction("Move to Start")
            move_to_end_action = menu.addAction("Move to End")

            # Connect actions
            move_left_action.triggered.connect(lambda: self.product_reordered.emit(self.product.id, "move_left"))
            move_right_action.triggered.connect(lambda: self.product_reordered.emit(self.product.id, "move_right"))
            move_to_start_action.triggered.connect(lambda: self.product_reordered.emit(self.product.id, "move_to_start"))
            move_to_end_action.triggered.connect(lambda: self.product_reordered.emit(self.product.id, "move_to_end"))

            # Show menu
            menu.exec(global_pos)

        except Exception as e:
            logger.error(f"Context menu error: {e}")

class AnimatedButton(QPushButton):
    def __init__(self, original_icon_size: QSize, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.animation = QPropertyAnimation(self, b"iconSize")
        self.animation.setDuration(120)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        self.original_size = original_icon_size
        self.hover_size = QSize(int(self.original_size.width() * 1.15), int(self.original_size.height() * 1.15))
        self.pressed_size = QSize(int(self.original_size.width() * 0.9), int(self.original_size.height() * 0.9))

    def enterEvent(self, event):
        self.animate_to(self.hover_size)
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.animate_to(self.original_size)
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        self.animate_to(self.pressed_size)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        if self.underMouse():
            self.animate_to(self.hover_size)
        super().mouseReleaseEvent(event)

    def animate_to(self, size):
        self.animation.stop()
        self.animation.setStartValue(self.iconSize())
        self.animation.setEndValue(size)
        self.animation.start()

# --- Performance Cache System ---
@dataclass
class PerformanceCache:
    """Smart cache for expensive calculations that invalidates when data changes."""
    # Statistics cache
    total_used_grams: Optional[int] = None
    total_cost: Optional[float] = None
    total_length_meters: Optional[float] = None
    stock_value: Optional[float] = None
    printed_last_30_days: Optional[int] = None

    # Top filaments cache
    top_filaments: Optional[list[Filament]] = None
    low_stock_filaments: Optional[list[Filament]] = None

    # Used filament IDs cache
    used_filament_ids: Optional[set[str]] = None

    # Cache validity tracking
    _filaments_hash: Optional[str] = None
    _settings_hash: Optional[str] = None
    _products_hash: Optional[str] = None
    _print_history_hash: Optional[str] = None

    def _calculate_hash(self, data: Any) -> str:
        """Calculate a hash for data to detect changes."""
        import hashlib
        if isinstance(data, list):
            # For lists of objects, create hash from their string representation
            content = str(sorted([str(item) for item in data]))
        else:
            content = str(data)
        return hashlib.md5(content.encode()).hexdigest()

    def is_valid(self, filaments: list[Filament], settings: dict, products: list[Product], print_history: list[PrintLogEntry]) -> bool:
        """Check if cache is still valid by comparing hashes."""
        current_filaments_hash = self._calculate_hash([(f.id, f.used_grams, f.in_stock_grams, f.purchase_price) for f in filaments])
        current_settings_hash = self._calculate_hash(settings)
        current_products_hash = self._calculate_hash([(p.id, str(p.variations)) for p in products])
        current_history_hash = self._calculate_hash([(h.id, h.timestamp) for h in print_history[:10]])  # Only check recent entries

        if (self._filaments_hash != current_filaments_hash or
            self._settings_hash != current_settings_hash or
            self._products_hash != current_products_hash or
            self._print_history_hash != current_history_hash):
            return False
        return True

    def update_hashes(self, filaments: list[Filament], settings: dict, products: list[Product], print_history: list[PrintLogEntry]):
        """Update hashes to mark cache as current."""
        self._filaments_hash = self._calculate_hash([(f.id, f.used_grams, f.in_stock_grams, f.purchase_price) for f in filaments])
        self._settings_hash = self._calculate_hash(settings)
        self._products_hash = self._calculate_hash([(p.id, str(p.variations)) for p in products])
        self._print_history_hash = self._calculate_hash([(h.id, h.timestamp) for h in print_history[:10]])

    def invalidate(self):
        """Clear all cached values."""
        self.total_used_grams = None
        self.total_cost = None
        self.total_length_meters = None
        self.stock_value = None
        self.printed_last_30_days = None
        self.top_filaments = None
        self.low_stock_filaments = None
        self.used_filament_ids = None
        self._filaments_hash = None
        self._settings_hash = None
        self._products_hash = None
        self._print_history_hash = None

# Splash screen functionality moved to launcher.py

    def close_splash(self):
        self.message_timer.stop()
        self.close()

# --- Hauptfenster ---
class MainWindow(QMainWindow):
    def get_overlay_scrollbar_style(self) -> str:
        """Generate a truly overlay scrollbar that doesn't affect layout at all."""
        return """
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollArea > QWidget > QWidget {
                background: transparent;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 12px;
                margin: 0px;
                border: none;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                min-height: 30px;
                margin: 2px;
                border: none;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
            QScrollBar::handle:vertical:pressed {
                background: rgba(255, 255, 255, 0.7);
            }
            QScrollBar::add-line:vertical {
                height: 0px;
                width: 0px;
            }
            QScrollBar::sub-line:vertical {
                height: 0px;
                width: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """

    def get_external_scrollbar_style(self) -> str:
        """Generate style for external scrollbar that doesn't affect layout."""
        return """
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                margin: 0px;
                border: none;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 5px;
                min-height: 30px;
                margin: 1px;
                border: none;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
            QScrollBar::handle:vertical:pressed {
                background: rgba(255, 255, 255, 0.7);
            }
            QScrollBar::add-line:vertical {
                height: 0px;
                width: 0px;
            }
            QScrollBar::sub-line:vertical {
                height: 0px;
                width: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """

    def get_scrollbar_style(self, width: int) -> str:
        """Generate consistent overlay capsule-style scrollbar CSS that doesn't affect layout."""
        handle_radius = width // 2
        margin = 1
        return f"""
            QScrollArea {{
                border: none;
                background: transparent;
            }}
            QScrollArea > QWidget > QWidget {{
                background: transparent;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: {width}px;
                margin: 0px;
                border: none;
                position: absolute;
                right: 2px;
                top: 0px;
                bottom: 0px;
            }}
            QScrollBar::handle:vertical {{
                background: rgba(255, 255, 255, 0.4);
                border-radius: {handle_radius - margin}px;
                min-height: 30px;
                margin: {margin}px;
                border: none;
            }}
            QScrollBar::handle:vertical:hover {{
                background: rgba(255, 255, 255, 0.6);
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: {width}px;
                margin: 0px;
                border: none;
                position: absolute;
                left: 0px;
                right: 0px;
                bottom: 2px;
            }}
            QScrollBar::handle:horizontal {{
                background: rgba(255, 255, 255, 0.4);
                border-radius: {handle_radius - margin}px;
                min-width: 30px;
                margin: {margin}px;
                border: none;
            }}
            QScrollBar::handle:horizontal:hover {{
                background: rgba(255, 255, 255, 0.6);
            }}
            QScrollBar::handle:vertical:pressed {{
                background: rgba(255, 255, 255, 0.8);
            }}
            QScrollBar::handle:horizontal:pressed {{
                background: rgba(255, 255, 255, 0.8);
            }}
            QScrollBar::add-line:vertical {{
                height: 0px;
                width: 0px;
            }}
            QScrollBar::sub-line:vertical {{
                height: 0px;
                width: 0px;
            }}
            QScrollBar::add-line:horizontal {{
                height: 0px;
                width: 0px;
            }}
            QScrollBar::sub-line:horizontal {{
                height: 0px;
                width: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: transparent;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: transparent;
            }}
        """

    def __init__(self, preloaded_data=None):
        """
        Initialize MainWindow with optional preloaded data to eliminate startup flashes.

        Args:
            preloaded_data (dict, optional): Dictionary containing:
                - 'settings': Loaded settings
                - 'filaments': Loaded filaments list
                - 'products': Loaded products list
                - 'print_history': Loaded print history list
        """
        self.preloaded_data = preloaded_data or {}
        super().__init__(); self.setWindowTitle("HueStock"); self.selected_filament_id = None; self.row_widgets = {}

        # Ensure Data and Data/products folders exist on every startup
        copy_initial_files()

        # Set window icon - try multiple locations and formats
        icon_loaded = False

        # Try .ico first (better for Windows) - check both external and embedded locations
        icon_paths = [
            APP_DIR / "appicon.ico",
            Path(__file__).parent / "appicon.ico",
            APP_DIR / "appicon.png",
            Path(__file__).parent / "appicon.png"
        ]

        # If running from PyInstaller bundle, also check embedded resources
        if hasattr(sys, '_MEIPASS'):
            icon_paths.extend([
                Path(sys._MEIPASS) / "appicon.ico",
                Path(sys._MEIPASS) / "appicon.png"
            ])
        # If running from Nuitka bundle, check executable directory
        elif hasattr(sys, 'frozen') and sys.frozen:
            exe_dir = Path(sys.executable).parent
            icon_paths.extend([
                exe_dir / "appicon.ico",
                exe_dir / "appicon.png"
            ])

        for icon_path in icon_paths:
            if icon_path.exists():
                try:
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        # Also set application icon for taskbar
                        QApplication.instance().setWindowIcon(icon)
                        icon_loaded = True
                        logger.info(f"Window icon loaded from: {icon_path}")
                        break
                except Exception as e:
                    logger.warning(f"Failed to load icon from {icon_path}: {e}")

        if not icon_loaded:
            logger.warning("No window icon could be loaded")

        # Blur functionality removed for research

        # Initialize performance cache
        self.performance_cache = PerformanceCache()





        # PERFORMANCE OPTIMIZATION: Use preloaded data if available
        if self.preloaded_data:
            self.settings = self.preloaded_data.get('settings', load_settings())
            self.filaments = self.preloaded_data.get('filaments', load_data())
            self.products = self.preloaded_data.get('products', load_products())
            self.print_history = self.preloaded_data.get('print_history', load_print_history())
        else:
            # Fallback to loading data normally
            self.settings = load_settings()
            self.filaments = load_data()
            self.products = load_products()
            self.print_history = load_print_history()
        self.current_theme_name = self.settings.get("theme", "Dark Modern")
        self.selected_product_id = None
        self.product_tile_widgets = {}
        self.notifications: list[Notification] = []
        self.notification_level: Optional[str] = None # "yellow" or "red"
        self.current_search_query = ""
        self.current_sort_key = ("brand", False)

        self.stacked_widget = QStackedWidget(); self.setCentralWidget(self.stacked_widget)

        # PERFORMANCE OPTIMIZATION: Lazy page creation
        # Only create the main table page immediately, defer others until needed
        self._pages_created = {"table": False, "settings": False, "shop": False}

        # Create placeholder widgets for lazy loading
        self.table_page = None
        self.settings_page = None
        self.shop_page = None

        # Create main table page immediately (user sees this first)
        self.table_page = self.create_table_page()
        self.stacked_widget.addWidget(self.table_page)
        self._pages_created["table"] = True

        # Add placeholder widgets for other pages (will be replaced when accessed)
        placeholder_settings = QWidget()
        placeholder_shop = QWidget()
        self.stacked_widget.addWidget(placeholder_settings)
        self.stacked_widget.addWidget(placeholder_shop)

        self.setup_notification_animation()

        # PERFORMANCE OPTIMIZATION: If data is preloaded, create widgets immediately
        if self.preloaded_data:
            self._deferred_init_done = True
            # Create widgets immediately since data is already loaded
            self._create_widgets_immediately()
        else:
            # Flag to track if deferred initialization has been done
            self._deferred_init_done = False

        self.center_and_resize()

    def _create_widgets_immediately(self):
        """
        PERFORMANCE OPTIMIZATION: Create widgets immediately when data is preloaded.
        This eliminates the popup flashes by having everything ready before the window shows.
        """
        # ANTI-FLASH PROTECTION: Disable all updates during widget creation
        self.setUpdatesEnabled(False)

        # Create all filament widgets at once
        for f in self.filaments:
            row_widget = FilamentRowWidget(f)
            row_widget.clicked.connect(self.on_row_clicked)
            row_widget.adjust_clicked.connect(self.open_adjust_dialog)
            row_widget.notes_clicked.connect(self.open_notes_dialog)
            self.row_widgets[f.id] = row_widget

        # Display them immediately (no flashes since window isn't shown yet)
        self.display_sorted_and_filtered_filaments()

        # Update notifications
        self.update_notifications()

        # Re-enable updates (will be properly shown later by splash screen)
        self.setUpdatesEnabled(True)

    def _deferred_initialization(self):
        """
        PERFORMANCE OPTIMIZATION: Heavy operations deferred until after window is shown.
        This dramatically improves startup time by avoiding blocking the UI thread.
        """
        # Load and display filaments (creates widgets)
        self.load_and_display_filaments()

        # Update notifications (involves calculations)
        self.update_notifications()

    def _ensure_page_created(self, page_index: int):
        """
        PERFORMANCE OPTIMIZATION: Lazy page creation.
        Only create pages when they're actually accessed.
        """
        if page_index == 1 and not self._pages_created["settings"]:
            # Create settings page
            self.settings_page = self.create_settings_page()
            self.stacked_widget.removeWidget(self.stacked_widget.widget(1))
            self.stacked_widget.insertWidget(1, self.settings_page)
            self._pages_created["settings"] = True

            # Initialize notes area with saved content
            if hasattr(self, 'notes_area'):
                self.notes_area.setText(self.settings.get("user_notes", ""))

        elif page_index == 2 and not self._pages_created["shop"]:
            # Create shop page
            self.shop_page = self.create_shop_page()
            self.stacked_widget.removeWidget(self.stacked_widget.widget(2))
            self.stacked_widget.insertWidget(2, self.shop_page)
            self._pages_created["shop"] = True








    def remove_filament_dismissal(self, filament_id: str):
        """Remove a filament from the dismissals list."""
        if "dismissals" not in self.settings:
            return
        if filament_id in self.settings["dismissals"]:
            del self.settings["dismissals"][filament_id]
            save_settings(self.settings)
            self.update_notifications()

    def setup_notification_animation(self):
        s_icon_size = int(24 * SCALE_FACTOR)
        original_icon_size = QSize(s_icon_size, s_icon_size)
        pulsing_icon_size = QSize(int(s_icon_size * 1.25), int(s_icon_size * 1.25))

        self.notification_anim_group = QSequentialAnimationGroup(self)
        anim_out = QPropertyAnimation(self.notification_btn, b"iconSize")
        anim_out.setStartValue(original_icon_size)
        anim_out.setEndValue(pulsing_icon_size)
        anim_out.setDuration(600)
        anim_out.setEasingCurve(QEasingCurve.Type.InOutQuad)

        anim_in = QPropertyAnimation(self.notification_btn, b"iconSize")
        anim_in.setStartValue(pulsing_icon_size)
        anim_in.setEndValue(original_icon_size)
        anim_in.setDuration(600)
        anim_in.setEasingCurve(QEasingCurve.Type.InOutQuad)

        self.notification_anim_group.addAnimation(anim_out)
        self.notification_anim_group.addAnimation(anim_in)
        self.notification_anim_group.setLoopCount(-1)

    # Blur functionality removed for research and future reimplementation

    def resizeEvent(self, event):
        """Handle window resize to update product tile sizes"""
        super().resizeEvent(event)
        # Update shop page if it's currently visible
        if hasattr(self, 'stacked_widget') and self.stacked_widget.currentIndex() == 2:
            # Use a timer to debounce resize events and prevent flashing
            if hasattr(self, 'resize_timer'):
                self.resize_timer.stop()
            else:
                self.resize_timer = QTimer()
                self.resize_timer.setSingleShot(True)
                self.resize_timer.timeout.connect(self.update_shop_page)
            self.resize_timer.start(150)  # Wait 150ms after resize stops

    def showEvent(self, event):
        """Override showEvent to trigger deferred initialization."""
        super().showEvent(event)

        # PERFORMANCE OPTIMIZATION: Do deferred initialization on first show
        if not self._deferred_init_done:
            self._deferred_init_done = True
            # Use QTimer with a small delay to ensure window is fully shown
            QTimer.singleShot(50, self._deferred_initialization)

    def save_notes(self):
        self.settings["user_notes"] = self.notes_area.toPlainText()
        save_settings(self.settings)

    def create_table_page(self) -> QWidget:
        table_page_widget = QWidget()
        main_layout = QVBoxLayout(table_page_widget)
        s_margin = int(BASE_MARGIN * SCALE_FACTOR)
        main_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        main_layout.setSpacing(int(10 * SCALE_FACTOR))

        title_layout = QHBoxLayout()
        title_layout.setSpacing(int(10 * SCALE_FACTOR))
        icon_label = QLabel()
        icon_path = get_appicon_path()
        if icon_path.exists():
            # Load with HiDPI support for crisp rendering
            screen = QApplication.primaryScreen()
            pixel_ratio = screen.devicePixelRatio() if screen else 1.0
            s_title_icon_size = int(s_title_font_size * 1.5)

            # Scale target size by device pixel ratio for high-DPI displays
            target_device_size = int(s_title_icon_size * pixel_ratio)

            pixmap = QPixmap(str(icon_path))
            scaled_pixmap = pixmap.scaled(target_device_size, target_device_size,
                                        Qt.AspectRatioMode.KeepAspectRatio,
                                        Qt.TransformationMode.SmoothTransformation)

            # Set device pixel ratio for proper display on HiDPI screens
            scaled_pixmap.setDevicePixelRatio(pixel_ratio)
            icon_label.setPixmap(scaled_pixmap)
        title = QLabel("HueStock")
        title.setObjectName("titleLabel")
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title)
        title_layout.addStretch()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by brand, type, color...")
        self.search_input.setObjectName("search_input")
        self.search_input.textChanged.connect(self.on_search_text_changed)
        self.search_input.setMinimumHeight(int(40 * SCALE_FACTOR))
        self.search_input.setMaximumWidth(int(350 * SCALE_FACTOR))
        filter_button = QPushButton()
        filter_button.setIcon(load_svg_icon('list-filter-plus', '#B0B0B0'))
        filter_button.setFixedSize(QSize(int(40 * SCALE_FACTOR), int(40 * SCALE_FACTOR)))
        filter_button.setObjectName("filterButton")
        filter_button.clicked.connect(self.show_sort_menu)

        # New book button next to filter button
        book_button = QPushButton()
        book_button.setIcon(load_svg_icon('book-open-text', '#B0B0B0'))
        book_button.setFixedSize(QSize(int(40 * SCALE_FACTOR), int(40 * SCALE_FACTOR)))
        book_button.setObjectName("filterButton")  # Use same styling as filter button
        book_button.clicked.connect(self.show_book_dialog)

        title_layout.addWidget(self.search_input)
        title_layout.addWidget(filter_button)
        title_layout.addWidget(book_button)
        main_layout.addLayout(title_layout)

        header_widget = self.create_header(); main_layout.addWidget(header_widget)
        list_frame = QWidget(); list_frame.setObjectName("listFrame")
        list_layout = QVBoxLayout(list_frame); list_layout.setContentsMargins(0, 0, 0, 0)
        self.scroll_area = QScrollArea(); self.scroll_area.setWidgetResizable(True); self.scroll_area.setObjectName("scrollArea")
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Apply auto-hiding scrollbar styling
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 6px;
                border-radius: 3px;
                margin: 15px 0px 15px 0px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                min-height: 15px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # Set up auto-hiding scrollbar with timer
        self.scrollbar_hide_timer = QTimer()
        self.scrollbar_hide_timer.setSingleShot(True)
        self.scrollbar_hide_timer.timeout.connect(self.hide_scrollbar)

        # Initially hide scrollbar
        self.scroll_area.verticalScrollBar().hide()

        # Connect scroll events to show scrollbar temporarily
        self.scroll_area.verticalScrollBar().valueChanged.connect(self.show_scrollbar_temporarily)
        self.scroll_area.installEventFilter(self)
        self.scroll_content_widget = QWidget()
        self.rows_layout = QVBoxLayout(self.scroll_content_widget)
        s_row_margin = int(5 * SCALE_FACTOR)
        self.rows_layout.setContentsMargins(0, s_row_margin, 0, s_row_margin)
        self.rows_layout.setSpacing(s_row_margin)
        self.rows_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scroll_area.setWidget(self.scroll_content_widget)

        list_layout.addWidget(self.scroll_area)
        main_layout.addWidget(list_frame, 1)

        buttons_layout = QHBoxLayout()
        settings_btn = self.create_action_button('chart-pie', '#0072BB', "settingsButton", icon_color='white')
        settings_btn.clicked.connect(lambda: self.switch_page(1))
        buttons_layout.addWidget(settings_btn)
        shop_btn = self.create_action_button('store', '#1E91D6', 'shopButton', icon_color='white')
        shop_btn.clicked.connect(lambda: self.switch_page(2))
        buttons_layout.addWidget(shop_btn)
        self.notification_btn = self.create_action_button('bell', '#50A0D1', 'notificationButton', icon_color='white', icon_size=24)
        self.notification_btn.clicked.connect(self.show_notifications)
        buttons_layout.addWidget(self.notification_btn)
        buttons_layout.addStretch()
        self.add_btn = self.create_action_button('square-pen', '#28a745', "addButton")
        self.edit_btn = self.create_action_button('settings-2', '#d39e00', "editButton")
        self.delete_btn = self.create_action_button('trash', '#dc3545', "deleteButton")
        self.add_btn.clicked.connect(self.add_filament)
        self.edit_btn.clicked.connect(self.edit_filament)
        self.delete_btn.clicked.connect(self.delete_filament)
        buttons_layout.addWidget(self.add_btn); buttons_layout.addWidget(self.edit_btn); buttons_layout.addWidget(self.delete_btn)
        main_layout.addLayout(buttons_layout)
        return table_page_widget

    def create_settings_page(self) -> QWidget:
        settings_page_widget = QWidget()
        page_layout = QGridLayout(settings_page_widget)
        s_margin = int(BASE_MARGIN * SCALE_FACTOR)
        page_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        page_layout.setSpacing(int(15 * SCALE_FACTOR))

        # --- Statistics Grid ---
        stats_grid = QGridLayout()
        stats_grid.setSpacing(int(15 * SCALE_FACTOR))

        usage_card, self.total_usage_label = self._create_stat_card('fa5s.weight-hanging', '#17a2b8', "Total Usage")
        kg_card, self.total_kg_usage_label = self._create_stat_card('fa5s.calendar-alt', '#17a2b8', "Printed in the last 30 days")
        rolls_card, self.rolls_used_label = self._create_stat_card_with_info('fa5s.ruler', '#17a2b8', "Length Printed", self.show_length_info)
        cost_card, self.total_cost_label = self._create_stat_card('fa5s.file-invoice-dollar', '#28a745', "Printed Value")
        value_card, self.stock_value_label = self._create_stat_card('fa5s.boxes', '#28a745', "Value of Stock")
        printed_card, self.products_printed_label = self._create_stat_card('fa5s.print', '#f0ad4e', "Products Printed")

        stats_grid.addWidget(usage_card, 0, 0)
        stats_grid.addWidget(kg_card, 0, 1)
        stats_grid.addWidget(rolls_card, 0, 2)
        stats_grid.addWidget(cost_card, 1, 0)
        stats_grid.addWidget(value_card, 1, 1)
        stats_grid.addWidget(printed_card, 1, 2)

        # --- Right Column ---
        right_column = QVBoxLayout()
        right_column.setSpacing(int(15 * SCALE_FACTOR))

        # Top list card (ScrollArea ohne sichtbaren Scrollbalken, Größe bleibt wie Frame)
        top_list_card = QFrame(); top_list_card.setObjectName("dashboardCard")
        top_list_v_layout = QVBoxLayout(top_list_card)
        top_list_v_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        top_list_title = QLabel("Most Used Filaments"); top_list_title.setObjectName("subTitleLabel")
        top_list_v_layout.addWidget(top_list_title)
        self.top_list_scroll = QScrollArea()
        self.top_list_scroll.setObjectName("topListScrollArea")
        self.top_list_scroll.setWidgetResizable(True)
        self.top_list_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.top_list_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # Die ScrollArea füllt das Feld, Größe wird durch das Layout bestimmt
        top_list_content = QWidget()
        top_list_content.setObjectName("topListScrollContent")
        self.top_list_layout = QVBoxLayout(top_list_content)
        self.top_list_layout.setSpacing(int(10 * SCALE_FACTOR))
        self.top_list_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.top_list_scroll.setWidget(top_list_content)
        top_list_v_layout.addWidget(self.top_list_scroll)

        # Low stock card (ScrollArea ohne sichtbaren Scrollbalken, Größe bleibt wie Frame)
        low_stock_card = QFrame(); low_stock_card.setObjectName("dashboardCard")
        low_stock_v_layout = QVBoxLayout(low_stock_card)
        low_stock_v_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        low_stock_title_layout = QHBoxLayout()
        low_stock_title_label = QLabel("Soon Out Of Stock"); low_stock_title_label.setObjectName("subTitleLabel")
        low_stock_settings_btn = QPushButton(); low_stock_settings_btn.setIcon(qta.icon('fa5s.cog', color='#B0B0B0'))
        low_stock_settings_btn.setFixedSize(QSize(int(24*SCALE_FACTOR), int(24*SCALE_FACTOR))); low_stock_settings_btn.setObjectName("transparentButton")
        low_stock_settings_btn.clicked.connect(self.change_low_stock_threshold)
        low_stock_title_layout.addWidget(low_stock_title_label)
        low_stock_title_layout.addStretch()
        low_stock_title_layout.addWidget(low_stock_settings_btn)
        self.low_stock_scroll = QScrollArea()
        self.low_stock_scroll.setObjectName("lowStockScrollArea")
        self.low_stock_scroll.setWidgetResizable(True)
        self.low_stock_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.low_stock_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        low_stock_content = QWidget()
        low_stock_content.setObjectName("lowStockScrollContent")
        self.low_stock_layout = QVBoxLayout(low_stock_content)
        self.low_stock_layout.setSpacing(int(5 * SCALE_FACTOR))
        self.low_stock_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.low_stock_scroll.setWidget(low_stock_content)
        low_stock_v_layout.addLayout(low_stock_title_layout)
        low_stock_v_layout.addWidget(self.low_stock_scroll)

        right_column.addWidget(top_list_card, 163)
        right_column.addWidget(low_stock_card, 200)

        # --- Bottom Left: Notes Card ---
        notes_card = QFrame(); notes_card.setObjectName("dashboardCard")
        notes_layout = QVBoxLayout(notes_card)
        notes_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        notes_title = QLabel("My Notes"); notes_title.setObjectName("subTitleLabel")
        self.notes_area = QTextEdit()
        self.notes_area.setObjectName("notesFieldDashboard")
        self.notes_area.setPlaceholderText("Add some notes, it will be saved automatically.")
        self.notes_save_timer = QTimer(self)
        self.notes_save_timer.setSingleShot(True)
        self.notes_save_timer.setInterval(1500)
        self.notes_save_timer.timeout.connect(self.save_notes)
        self.notes_area.textChanged.connect(self.notes_save_timer.start)
        notes_layout.addWidget(notes_title)
        notes_layout.addWidget(self.notes_area)

        # --- Page Assembly ---
        left_content = QWidget()
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0,0,0,0)
        left_layout.addLayout(stats_grid)
        left_layout.addStretch(19)
        left_layout.addWidget(notes_card)
        left_layout.setStretchFactor(stats_grid, 100)
        left_layout.setStretchFactor(notes_card, 600)

        page_layout.addWidget(left_content, 0, 0)
        page_layout.addLayout(right_column, 0, 1)

        # --- Bottom Controls ---
        bottom_left_layout = QHBoxLayout()
        self.stats_back_button = self.create_action_button('arrow-left', '#555', "backButton")
        self.stats_back_button.clicked.connect(lambda: self.switch_page(0))
        bottom_title = QLabel("Statistics & Insights"); bottom_title.setObjectName("titleLabel")
        bottom_left_layout.addWidget(self.stats_back_button); bottom_left_layout.addSpacing(int(10 * SCALE_FACTOR))
        bottom_left_layout.addWidget(bottom_title); bottom_left_layout.addStretch()

        bottom_right_layout = QHBoxLayout()

        # Theme selection
        self.theme_combo = QComboBox()
        self.theme_combo.setObjectName("themeCombo")
        for theme_name in THEMES.keys():
            self.theme_combo.addItem(theme_name)
        self.theme_combo.setCurrentText(self.settings.get("theme", "Dark Modern"))
        self.theme_combo.currentTextChanged.connect(self.handle_theme_change)
        self.theme_combo.setToolTip("Select app theme")

        self.currency_input = QLineEdit(self.settings.get("currency_symbol", "€"))
        self.currency_input.setMaxLength(3)
        self.currency_input.setObjectName("currencyInputCapsule")
        self.currency_input.setFixedWidth(int(50 * SCALE_FACTOR))
        self.currency_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.currency_input.setToolTip("Enter your currency symbol (max 3 chars)")
        self.currency_input.editingFinished.connect(self.handle_currency_change)

        # Info button for app information
        info_button = QPushButton()
        info_button.setIcon(qta.icon('fa5s.info-circle', color='#B0B0B0'))
        info_button.setFixedWidth(int(50 * SCALE_FACTOR))  # Match currency input width
        info_button.setObjectName("infoButton")
        info_button.setToolTip("About HueStock")
        info_button.clicked.connect(self.show_app_info)

        bottom_right_layout.addStretch()
        bottom_right_layout.addWidget(self.theme_combo)
        bottom_right_layout.addSpacing(int(10 * SCALE_FACTOR))
        bottom_right_layout.addWidget(self.currency_input)
        bottom_right_layout.addSpacing(int(10 * SCALE_FACTOR))
        bottom_right_layout.addWidget(info_button)

        page_layout.addLayout(bottom_left_layout, 1, 0)
        page_layout.addLayout(bottom_right_layout, 1, 1)

        page_layout.setColumnStretch(0, 6) # Give more space to left side
        page_layout.setColumnStretch(1, 4) # Less space to right
        page_layout.setRowStretch(0, 1)
        page_layout.setRowStretch(1, 0)

        return settings_page_widget

    def calculate_optimal_product_tile_size(self) -> tuple[int, int]:
        """Calculate optimal product tile size to fill horizontal space with 6 tiles and appropriate gaps"""
        # Get current window width
        window_width = self.width()

        # Account for margins and spacing
        s_margin = int(BASE_MARGIN * SCALE_FACTOR)  # Page margins (left + right)

        # Available width = window_width - (left margin + right margin)
        available_width = window_width - (2 * s_margin)

        # Calculate tile width: available_width = (6 * tile_width) + (5 * gap_size)
        # We want nice proportional gaps, so let's use 1/8 of tile width as gap
        # So: available_width = (6 * tile_width) + (5 * tile_width/8)
        # available_width = tile_width * (6 + 5/8) = tile_width * 6.625
        calculated_width = int(available_width / 6.625)

        # Ensure minimum reasonable size (don't go below 140px width)
        min_width = int(140 * SCALE_FACTOR)
        tile_width = max(calculated_width, min_width)

        # Use a better aspect ratio for product tiles (more like original 150x220)
        # This gives approximately 1.47:1 ratio instead of 4:3
        tile_height = int(tile_width * 1.47)

        return tile_width, tile_height

    def create_shop_page(self) -> QWidget:
        shop_page_widget = QWidget()
        page_layout = QVBoxLayout(shop_page_widget)
        s_margin = int(BASE_MARGIN * SCALE_FACTOR)
        page_layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        page_layout.setSpacing(int(10 * SCALE_FACTOR))

        scroll_area = QScrollArea(); scroll_area.setWidgetResizable(True); scroll_area.setObjectName("scrollArea")
        # Hide both horizontal and vertical scrollbars in shop page
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_content_widget = QWidget()
        self.product_grid_layout = QGridLayout(scroll_content_widget)
        # Calculate proportional spacing for 6 products per row
        tile_width, _ = self.calculate_optimal_product_tile_size()
        gap_size = int(tile_width / 8)  # Gap is 1/8 of tile width
        self.product_grid_layout.setSpacing(gap_size)
        self.product_grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignHCenter)
        scroll_area.setWidget(scroll_content_widget)

        bottom_layout = QHBoxLayout()
        self.shop_back_button = self.create_action_button('arrow-left', '#555', "backButton", icon_color='white')
        self.shop_back_button.clicked.connect(lambda: self.switch_page(0))

        shop_title = QLabel("Shop"); shop_title.setObjectName("titleLabel")
        bottom_layout.addWidget(self.shop_back_button)
        bottom_layout.addSpacing(int(10 * SCALE_FACTOR))
        bottom_layout.addWidget(shop_title)
        bottom_layout.addStretch()
        self.add_product_btn = self.create_action_button('square-pen', '#28a745', "addButton")
        self.edit_product_btn = self.create_action_button('settings-2', '#d39e00', "editButton")
        self.delete_product_btn = self.create_action_button('trash', '#dc3545', "deleteButton")
        self.add_product_btn.clicked.connect(self.add_product)
        self.edit_product_btn.clicked.connect(self.edit_product)
        self.delete_product_btn.clicked.connect(self.delete_product)
        bottom_layout.addWidget(self.add_product_btn)
        bottom_layout.addWidget(self.edit_product_btn)
        bottom_layout.addWidget(self.delete_product_btn)
        page_layout.addWidget(scroll_area, 1)
        page_layout.addLayout(bottom_layout)
        return shop_page_widget

    def get_print_count_for_variation(self, variation: Variation) -> float:
        if not variation.components:
            return float('inf') # Can "print" it infinitely if it uses no filament

        min_prints = float('inf')
        for component in variation.components:
            filament = next((f for f in self.filaments if f.id == component.filament_id), None)
            if not filament:
                return 0 # Cannot find filament -> unprintable

            if component.grams_used == 0:
                continue # uses 0g, doesn't limit prints

            # How many times can we print this component?
            num_prints = filament.in_stock_grams / component.grams_used
            if num_prints < min_prints:
                min_prints = num_prints

        return min_prints

    def update_notifications(self):
        # --- 1. Garbage Collect Timed Dismissals ---
        now = datetime.now()
        active_dismissals = [
            d for d in self.settings.get('dismissed_notifications', [])
            if not (d.get('dismiss_until') and now >= datetime.fromisoformat(d['dismiss_until']))
        ]

        # --- 2. Remove "dismiss until change" entries where stock has actually changed ---
        remaining_dismissals = []
        for dismissal in active_dismissals:
            if dismissal.get('dismiss_until_change'):
                # This was dismissed "until change" - check if relevant data changed
                original_payload = dismissal.get('payload_at_dismiss', {})
                if original_payload.get('type') == 'filament':
                    filament_id = original_payload.get('id')
                    original_stock = original_payload.get('in_stock_grams')
                    # Find current filament and check if stock changed
                    current_filament = next((f for f in self.filaments if f.id == filament_id), None)
                    if current_filament and current_filament.in_stock_grams != original_stock:
                        # Stock changed - remove this dismissal
                        continue
                elif original_payload.get('type') == 'product':
                    # For products, check if any related stock changed
                    product_id = original_payload.get('id')
                    product = next((p for p in self.products if p.id == product_id), None)
                    # Prüfe, ob component_stocks_at_dismiss existiert (neues Format)
                    component_stocks_at_dismiss = dismissal.get("component_stocks_at_dismiss", [])
                    if component_stocks_at_dismiss:
                        stock_changed = False
                        for comp in component_stocks_at_dismiss:
                            filament_id = comp.get("filament_id")
                            original_stock = comp.get("in_stock_grams")
                            current_filament = next((f for f in self.filaments if f.id == filament_id), None)
                            if current_filament and current_filament.in_stock_grams != original_stock:
                                stock_changed = True
                                break
                        if stock_changed:
                            continue
                    elif product:
                        # Fallback für alte Dismissals: wie bisher, aber markiere als "changed" sobald irgendein Filament existiert
                        stock_changed = False
                        for variation in product.variations:
                            for component in variation.components:
                                filament = next((f for f in self.filaments if f.id == component.filament_id), None)
                                if filament:
                                    stock_changed = True
                                    break
                            if stock_changed:
                                break
                        if stock_changed:
                            continue
            # Keep this dismissal
            remaining_dismissals.append(dismissal)

        # Update settings if dismissals changed
        if len(remaining_dismissals) != len(self.settings.get('dismissed_notifications', [])):
            self.settings['dismissed_notifications'] = remaining_dismissals
            save_settings(self.settings)

        # --- 3. Create a Set of all currently active "Dismissed" Payloads for fast lookup ---
        dismissed_payloads_set = {
            json.dumps(d['payload_at_dismiss'], sort_keys=True)
            for d in remaining_dismissals
        }

        self.notifications.clear()

        # --- Used Filament Logic ---
        used_filament_ids = set()
        track_only_used = self.settings.get("track_only_used_in_products", False)
        if track_only_used:
            for product in self.products:
                for variation in product.variations:
                    for component in variation.components:
                        used_filament_ids.add(component.filament_id)
        filaments_to_check = [f for f in self.filaments if not track_only_used or f.id in used_filament_ids]

        # --- 3. Generate All Potential Notifications and check against the dismissed set ---

        # --- Filament Notifications ---
        for f in filaments_to_check:
            # The payload uniquely identifies the problem state
            current_payload = {'type': 'filament', 'id': f.id, 'in_stock_grams': f.in_stock_grams}
            current_payload_str = json.dumps(current_payload, sort_keys=True)

            if current_payload_str in dismissed_payloads_set:
                continue  # This exact state is dismissed, so skip generating a notification.

            filament_name = f"{f.brand} {f.type} {f.color}"
            if f.in_stock_grams <= self.settings["critical_stock_threshold"]:
                msg = f"{filament_name} is empty!" if f.in_stock_grams == 0 else f"{filament_name} very low! ({f.in_stock_grams}g)"
                self.notifications.append(Notification(msg, "red", "filament", f.id, current_payload))
            elif f.in_stock_grams <= self.settings["low_stock_threshold"]:
                msg = f"{filament_name} is low! ({f.in_stock_grams}g)"
                self.notifications.append(Notification(msg, "yellow", "filament", f.id, current_payload))

        # --- Product Notifications ---
        for p in self.products:
            all_stock_warnings = self.check_product_stock(p)

            # Handle "Insufficient Stock" (red) notifications
            for detailed_warning_msg in all_stock_warnings:
                current_payload = {'type': 'product', 'id': p.id, 'warning': detailed_warning_msg}
                current_payload_str = json.dumps(current_payload, sort_keys=True)

                if current_payload_str in dismissed_payloads_set:
                    continue

                variation_name = detailed_warning_msg.split('(')[1].split(')')[0] if '(' in detailed_warning_msg and ')' in detailed_warning_msg else ""
                user_friendly_msg = f"No stock left for '{p.name} | {variation_name}'"
                self.notifications.append(Notification(user_friendly_msg, "red", "product", p.id, current_payload))

            # Handle "Low Print Count" (yellow) only if there are NO red warnings
            if not all_stock_warnings:
                max_prints = sum(self.get_print_count_for_variation(var) for var in p.variations)
                if max_prints <= 2 and max_prints > 0:
                    current_payload = {'type': 'product', 'id': p.id, 'max_prints': max_prints}
                    current_payload_str = json.dumps(current_payload, sort_keys=True)

                    if current_payload_str in dismissed_payloads_set:
                        continue

                    if max_prints == 1:
                        user_friendly_msg = f"Can only print '{p.name}' one more time."
                    else:
                        user_friendly_msg = f"Can only print '{p.name}' {int(max_prints)} more times."

                    self.notifications.append(Notification(user_friendly_msg, "yellow", "product", p.id, current_payload))

        # --- 4. Finalize UI ---
        if any(msg.level == "red" for msg in self.notifications):
            self.notification_level = "red"
        elif any(msg.level == "yellow" for msg in self.notifications):
            self.notification_level = "yellow"
        else:
            self.notification_level = None
        self.update_notification_bell_icon()

    def update_notification_bell_icon(self):
        s_icon_size = int(24 * SCALE_FACTOR)
        theme = THEMES.get(self.settings.get("theme", "Dark Modern"), THEMES["Dark Modern"])
        bg_color = theme.get('notification_btn', '#50A0D1') # Theme-aware base color with fallback
        should_animate = bool(self.notification_level)

        if self.notification_level == "red":
            bg_color = theme.get('notification_red', '#942421') # Theme-aware red with fallback
        elif self.notification_level == "yellow":
            bg_color = theme.get('notification_yellow', '#A87900') # Theme-aware yellow with fallback

        # Use bell-plus icon if there are notifications, otherwise regular bell
        icon_name = 'bell-plus' if self.notifications else 'bell'
        self.notification_btn.setIcon(load_svg_icon(icon_name, 'white'))
        self.notification_btn.setIconSize(QSize(s_icon_size, s_icon_size))
        self.notification_btn.setStyleSheet(f"background-color: {bg_color};")

        if should_animate:
            if self.notification_anim_group.state() != QAbstractAnimation.State.Running:
                self.notification_anim_group.start()
        else:
            if self.notification_anim_group.state() == QAbstractAnimation.State.Running:
                self.notification_anim_group.stop()
            # Reset icon size when stopping to prevent it getting stuck in a large state
            self.notification_btn.setIconSize(QSize(s_icon_size, s_icon_size))

    def show_notifications(self):
        popup = NotificationPopup(self.notifications, self)
        popup.dismiss_requested.connect(self.handle_dismiss_notification)
        btn_pos = self.notification_btn.mapToGlobal(QPoint(0, 0))
        # Position popup next to the button (to the right) and above it
        popup.adjustSize() # Let the popup determine its size first
        popup_x = btn_pos.x() + self.notification_btn.width() + 5  # 5px margin
        popup_y = btn_pos.y() - popup.height() + self.notification_btn.height()  # Position above button
        popup.show_at_with_animation(QPoint(int(popup_x), int(popup_y)))

    def handle_dismiss_notification(self, notification: Notification):
        try:
            # Close any active popups first - use safe iteration
            popups_to_close = []
            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, NotificationPopup):
                    popups_to_close.append(widget)

            # Close popups safely
            for popup in popups_to_close:
                try:
                    popup.close()
                except RuntimeError:
                    # Widget already deleted, ignore
                    pass

            dialog = DismissDialog(notification, self)
            if dialog.exec():
                new_dismissal_data = dialog.get_data()
                if new_dismissal_data:
                    # Get the specific payload of the notification that was just dismissed
                    dismissed_payload = new_dismissal_data.get('payload_at_dismiss')

                    # Create a new list of dismissals, keeping only those that DO NOT match the one we just dismissed.
                    # This prevents the "ping-pong" effect by not touching other dismissals.
                    current_dismissals = self.settings.get('dismissed_notifications', [])
                    updated_dismissals = [
                        d for d in current_dismissals
                        if d.get('payload_at_dismiss') != dismissed_payload
                    ]

                    # Now, add the new dismissal entry (which contains the dismiss_until/until_change info)
                    updated_dismissals.append(new_dismissal_data)

                    self.settings['dismissed_notifications'] = updated_dismissals
                    save_settings(self.settings)

                    # Re-run the notification check with the updated dismissal list
                    self.update_notifications()
        except Exception as e:
            logger.error(f"Error handling notification dismissal: {e}")
            # Continue gracefully without crashing

    def check_product_stock(self, product: Product) -> List[str]:
        """
        Checks all variations of a product and returns a list of all stock warnings.
        Returns an empty list if there are no issues.
        """
        warnings = []
        if not product.variations:
            warnings.append(f"Product '{product.name}' has no variations defined.")
            return warnings
        if not any(var.components for var in product.variations):
            warnings.append(f"None of the variations for '{product.name}' have filaments assigned.")
            return warnings

        for variation in product.variations:
            if not variation.components:
                continue
            for component in variation.components:
                filament = next((f for f in self.filaments if f.id == component.filament_id), None)
                if not filament:
                    warnings.append(f"Missing filament for '{product.name}' ({variation.name}): A required filament could not be found.")
                    # Break inner loop to avoid multiple "missing" messages for the same variation
                    break
                if filament.in_stock_grams < component.grams_used:
                    warnings.append(
                        f"Insufficient stock for '{product.name}' ({variation.name}): "
                        f"Need {component.grams_used}g of '{filament.brand} {filament.color}', "
                        f"but only {filament.in_stock_grams}g available."
                    )
        return warnings

    def update_shop_page(self):
        for i in reversed(range(self.product_grid_layout.count())):
            item = self.product_grid_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
                    widget.deleteLater()
        self.product_tile_widgets.clear()

        # Calculate optimal tile size for current window
        optimal_tile_size = self.calculate_optimal_product_tile_size()

        row, col = 0, 0
        for product in self.products:
            tile = ProductTileWidget(product, optimal_tile_size)

            # Check for stock warnings (red warnings)
            stock_warnings = self.check_product_stock(product)
            if stock_warnings:
                # Join all warnings into one message
                warning_message = "\n".join(stock_warnings)
                tile.show_warning(warning_message)
            else:
                # Check for low print count (yellow warnings) only if no red warnings
                total_prints = sum(self.get_print_count_for_variation(var) for var in product.variations)
                if total_prints <= 2 and total_prints > 0:
                    warning_message = f"Can only print '{product.name}' {int(total_prints)} more time{'s' if total_prints != 1 else ''}."
                    tile.show_warning(warning_message)

            tile.clicked.connect(self.on_product_tile_clicked)
            tile.log_print_requested.connect(self.open_log_print_dialog)
            tile.product_reordered.connect(self.reorder_products)
            self.product_grid_layout.addWidget(tile, row, col)
            self.product_tile_widgets[product.id] = tile
            col += 1
            if col > 5:  # Changed to 5 to allow 6 columns (0-5)
                col = 0
                row += 1
        self.selected_product_id = None

    def reorder_products(self, source_product_id: str, action_or_target_id: str):
        """Reorder products by moving source product based on action or to target position"""
        # Find the source product index
        source_index = None
        for i, product in enumerate(self.products):
            if product.id == source_product_id:
                source_index = i
                break

        if source_index is None:
            return

        # Handle different reordering actions
        if action_or_target_id == "move_left":
            if source_index > 0:
                # Swap with previous product
                self.products[source_index], self.products[source_index - 1] = \
                    self.products[source_index - 1], self.products[source_index]
        elif action_or_target_id == "move_right":
            if source_index < len(self.products) - 1:
                # Swap with next product
                self.products[source_index], self.products[source_index + 1] = \
                    self.products[source_index + 1], self.products[source_index]
        elif action_or_target_id == "move_to_start":
            # Move to beginning
            product = self.products.pop(source_index)
            self.products.insert(0, product)
        elif action_or_target_id == "move_to_end":
            # Move to end
            product = self.products.pop(source_index)
            self.products.append(product)
        else:
            # Original drag-and-drop behavior: move to target position
            target_index = None
            for i, product in enumerate(self.products):
                if product.id == action_or_target_id:
                    target_index = i
                    break

            if target_index is not None:
                source_product = self.products.pop(source_index)
                self.products.insert(target_index, source_product)

        # Save the new order and update the UI
        save_products(self.products)
        self.update_shop_page()

    def on_product_tile_clicked(self, product_id: str):
        if self.selected_product_id == product_id:
            if product_id in self.product_tile_widgets:
                self.product_tile_widgets[product_id].set_selected(False)
            self.selected_product_id = None
        else:
            if self.selected_product_id and self.selected_product_id in self.product_tile_widgets:
                self.product_tile_widgets[self.selected_product_id].set_selected(False)
            self.selected_product_id = product_id
            if product_id in self.product_tile_widgets:
                self.product_tile_widgets[product_id].set_selected(True)

    def add_product(self):
        dialog = ProductDialog(self.filaments, all_products=self.products, parent=self)
        if not dialog.exec():
            return
        new_product = dialog.get_data()
        self.products.append(new_product)
        save_products(self.products)
        self.update_shop_page()
        self.update_notifications()

    def edit_product(self):
        if not self.selected_product_id:
            show_custom_message(self, 'fa5s.exclamation-circle', '#f0ad4e', "Selection Error", "Please select a product to edit.")
            return
        product_to_edit = next((p for p in self.products if p.id == self.selected_product_id), None)
        if not product_to_edit:
            return
        dialog = ProductDialog(self.filaments, product=copy.deepcopy(product_to_edit), parent=self)
        if dialog.exec():
            updated_product = dialog.get_data()
            for i, p in enumerate(self.products):
                if p.id == updated_product.id:
                    self.products[i] = updated_product
                    break
            save_products(self.products)
            self.update_shop_page()
            self.update_notifications()

    def delete_product(self):
        if not self.selected_product_id:
            show_custom_message(self, 'fa5s.exclamation-circle', '#f0ad4e', "Selection Error", "Please select a product to delete.")
            return
        if not ask_custom_question(self, "Confirm Deletion", "Are you sure you want to delete this product?"):
            return
        tile_to_delete = self.product_tile_widgets.get(self.selected_product_id)
        if not tile_to_delete:
            return
        self.animate_tile_deletion(tile_to_delete)

    def animate_tile_deletion(self, tile_to_delete):
        product_id_to_delete = tile_to_delete.product.id

        fade_anim = QPropertyAnimation(tile_to_delete, b"windowOpacity", self)
        fade_anim.setDuration(300)
        fade_anim.setEndValue(0)
        fade_anim.setEasingCurve(QEasingCurve.Type.InOutCubic)

        def on_finished():
            # Remove product from data source
            self.products = [p for p in self.products if p.id != product_id_to_delete]
            save_products(self.products)

            # Update dependent data
            self.update_notifications()
            if self.selected_product_id == product_id_to_delete:
                self.selected_product_id = None

            # Rebuild the entire grid, which handles reflow smoothly
            self.update_shop_page()

        fade_anim.finished.connect(on_finished)
        fade_anim.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def update_settings_page(self):
        """Update settings page with cached calculations for better performance."""
        # Only update if the settings page has been created
        if not self._pages_created.get("settings", False):
            return

        currency = self.settings.get("currency_symbol", "€")

        # Check cache validity and use cached values if possible
        if not self.performance_cache.is_valid(self.filaments, self.settings, self.products, self.print_history):
            # Cache is invalid, recalculate all values
            self._calculate_statistics()
            self.performance_cache.update_hashes(self.filaments, self.settings, self.products, self.print_history)

        # Use cached values for UI updates
        self.total_usage_label.setText(f"{self.performance_cache.total_used_grams:g}g")
        self.total_cost_label.setText(f"{self.performance_cache.total_cost:.2f} {currency}")
        self.stock_value_label.setText(f"{self.performance_cache.stock_value:.0f} {currency}")
        self.total_kg_usage_label.setText(f"{self.performance_cache.printed_last_30_days:g}g")
        self.rolls_used_label.setText(f"{self.performance_cache.total_length_meters:.1f}m")
        self.products_printed_label.setText(f"{self.settings.get('products_printed', 0)}")

        # Update list widgets using cached data
        self._update_top_filaments_list()
        self._update_low_stock_list()

    def _calculate_statistics(self):
        """Calculate and cache all expensive statistics."""
        # Basic statistics
        self.performance_cache.total_used_grams = sum(f.used_grams for f in self.filaments)
        self.performance_cache.total_cost = sum(f.cost_of_used for f in self.filaments)
        self.performance_cache.stock_value = sum(f.in_stock_grams * f.cost_per_gram() for f in self.filaments)

        # Calculate printed in last 30 days
        if not self.print_history:
            self.print_history = load_print_history()  # Load fresh data if needed
        thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
        recent_prints = [entry for entry in self.print_history if entry.timestamp >= thirty_days_ago]
        self.performance_cache.printed_last_30_days = sum(comp.grams_used for entry in recent_prints for comp in entry.components)

        # Calculate total length (1kg of 1.75mm filament = 335 meters)
        METERS_PER_KG = 335
        self.performance_cache.total_length_meters = (self.performance_cache.total_used_grams / 1000.0) * METERS_PER_KG

        # Cache top filaments (sorted by usage)
        self.performance_cache.top_filaments = sorted(self.filaments, key=lambda f: f.used_grams, reverse=True)[:3]

        # Cache used filament IDs for "track only used" feature
        if self.settings.get("track_only_used_in_products", False):
            used_filament_ids = set()
            for product in self.products:
                for variation in product.variations:
                    for component in variation.components:
                        used_filament_ids.add(component.filament_id)
            self.performance_cache.used_filament_ids = used_filament_ids
        else:
            self.performance_cache.used_filament_ids = None

        # Cache low stock filaments
        low_stock_candidates = [f for f in self.filaments if f.in_stock_grams <= self.settings["low_stock_threshold"]]
        if self.performance_cache.used_filament_ids:
            low_stock_candidates = [f for f in low_stock_candidates if f.id in self.performance_cache.used_filament_ids]
        self.performance_cache.low_stock_filaments = sorted(low_stock_candidates, key=lambda f: f.in_stock_grams)

    def _update_top_filaments_list(self):
        """Update the top filaments list using cached data."""
        # Clear existing widgets
        while self.top_list_layout.count():
            item = self.top_list_layout.takeAt(0)
            if item and item.widget():
                widget = item.widget()
                if widget:
                    widget.deleteLater()

        # Add cached top filaments
        if not self.performance_cache.top_filaments or not any(f.used_grams > 0 for f in self.performance_cache.top_filaments):
            self.top_list_layout.addWidget(QLabel("No usage logged yet."))
        else:
            for i, filament in enumerate(self.performance_cache.top_filaments):
                if filament.used_grams > 0:
                    self.top_list_layout.addWidget(TopFilamentWidget(i + 1, filament))
        self.top_list_layout.addStretch()

    def _update_low_stock_list(self):
        """Update the low stock list using cached data."""
        # Clear existing widgets
        while self.low_stock_layout.count():
            item = self.low_stock_layout.takeAt(0)
            if item and item.widget():
                widget = item.widget()
                if widget:
                    widget.deleteLater()

        # Add cached low stock filaments
        if not self.performance_cache.low_stock_filaments:
            self.low_stock_layout.addWidget(QLabel("All filaments are well stocked."))
        else:
            for filament in self.performance_cache.low_stock_filaments:
                warning_color = "#f0ad4e"
                if filament.in_stock_grams <= self.settings["critical_stock_threshold"]:
                    warning_color = "#d9534f"
                self.low_stock_layout.addWidget(WarningFilamentWidget(filament, warning_color))
        self.low_stock_layout.addStretch()

    def invalidate_cache(self):
        """Manually invalidate the performance cache when data changes."""
        self.performance_cache.invalidate()

    def handle_currency_change(self):
        new_symbol = self.currency_input.text().strip()
        if not new_symbol:
            new_symbol = "€"
            self.currency_input.setText(new_symbol)
        if self.settings.get("currency_symbol", "€") != new_symbol:
            self.settings["currency_symbol"] = new_symbol
            save_settings(self.settings)
            self.update_settings_page()

    def handle_theme_change(self, theme_name: str):
        """Handle theme change and apply new stylesheet."""
        if self.settings.get("theme", "Dark Modern") != theme_name:
            self.settings["theme"] = theme_name
            self.current_theme_name = theme_name
            save_settings(self.settings)

            # Apply new theme
            new_stylesheet = get_theme_stylesheet(theme_name)
            self.setStyleSheet(new_stylesheet)

            # Optimized: Update existing widgets instead of recreating all
            for filament in self.filaments:
                if filament.id in self.row_widgets:
                    self.update_single_filament_widget(filament)

            # Force update of all dialogs that might be open
            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, QDialog) and widget != self:
                    widget.setStyleSheet(new_stylesheet)

            # Update backButton styling for theme change
            self.update_back_button_styling()

    def update_back_button_styling(self):
        """Update backButton styling to match current theme."""
        theme = THEMES.get(self.settings.get("theme", "Dark Modern"), THEMES["Dark Modern"])
        back_button_style = f"""
            QPushButton {{
                background-color: {theme['surface']};
                border-radius: {s_radius}px;
                border: 2px solid {theme['border']};
            }}
            QPushButton:hover {{
                background-color: {theme['hover']};
                border-color: {theme['primary']};
            }}
            QPushButton:pressed {{
                background-color: {theme['border']};
                border-color: {theme['primary']};
            }}
        """

        # Update both backButtons if they exist
        if hasattr(self, 'stats_back_button'):
            self.stats_back_button.setStyleSheet(back_button_style)
        if hasattr(self, 'shop_back_button'):
            self.shop_back_button.setStyleSheet(back_button_style)

    def change_low_stock_threshold(self):
        dialog = LowStockSettingsDialog(self.settings, self)
        if dialog.exec():
            new_settings = dialog.get_values()
            if self.settings.get("low_stock_threshold") != new_settings["low_stock_threshold"] or \
               self.settings.get("critical_stock_threshold") != new_settings["critical_stock_threshold"] or \
               self.settings.get("track_only_used_in_products") != new_settings["track_only_used_in_products"]:
                self.settings.update(new_settings)
                save_settings(self.settings)
                self.update_settings_page()
                self.update_notifications()

    def switch_page(self, index):
        current_index = self.stacked_widget.currentIndex()
        if current_index == index:
            return

        # PERFORMANCE OPTIMIZATION: Ensure page is created before switching
        self._ensure_page_created(index)

        # Optimized: Only update the page we're switching TO, and only reload data if needed
        if index == 1:
            self.update_settings_page()
        if index == 2:
            # Only reload data if we haven't been on shop page recently
            # This avoids unnecessary file I/O when switching between pages
            self.update_shop_page()

        current_widget = self.stacked_widget.widget(current_index)
        next_widget = self.stacked_widget.widget(index)
        if not current_widget or not next_widget:
            self.stacked_widget.setCurrentIndex(index)
            return

        # Smooth fade animation using graphics effects
        animation_duration = 200  # Fast but smooth
        self.animate_smooth_fade(current_index, index, animation_duration)

    def animate_fade(self, current_index, next_index, duration):
        current_widget = self.stacked_widget.widget(current_index)
        next_widget = self.stacked_widget.widget(next_index)
        if not current_widget or not next_widget:
            self.stacked_widget.setCurrentIndex(next_index)
            return
        next_widget.setWindowOpacity(0)
        next_widget.show()
        next_widget.raise_()
        self.fade_out_anim = QPropertyAnimation(current_widget, b"windowOpacity", self)
        self.fade_out_anim.setStartValue(1); self.fade_out_anim.setEndValue(0)
        self.fade_out_anim.setDuration(duration); self.fade_out_anim.setEasingCurve(QEasingCurve.Type.OutQuad)
        self.fade_in_anim = QPropertyAnimation(next_widget, b"windowOpacity", self)
        self.fade_in_anim.setStartValue(0); self.fade_in_anim.setEndValue(1)
        self.fade_in_anim.setDuration(duration); self.fade_in_anim.setEasingCurve(QEasingCurve.Type.InQuad)
        self.page_anim_group = QParallelAnimationGroup(self)
        self.page_anim_group.addAnimation(self.fade_out_anim)
        self.page_anim_group.addAnimation(self.fade_in_anim)
        def on_finished():
            self.stacked_widget.setCurrentIndex(next_index)
            current_widget.hide()
            current_widget.setWindowOpacity(1)
        self.page_anim_group.finished.connect(on_finished)
        self.page_anim_group.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def animate_smooth_fade(self, current_index, next_index, duration):
        """Smooth crossfade animation using QGraphicsOpacityEffect for better performance"""
        from PySide6.QtWidgets import QGraphicsOpacityEffect

        current_widget = self.stacked_widget.widget(current_index)
        next_widget = self.stacked_widget.widget(next_index)
        if not current_widget or not next_widget:
            self.stacked_widget.setCurrentIndex(next_index)
            return

        # Create opacity effects
        self.current_opacity_effect = QGraphicsOpacityEffect()
        self.next_opacity_effect = QGraphicsOpacityEffect()

        current_widget.setGraphicsEffect(self.current_opacity_effect)
        next_widget.setGraphicsEffect(self.next_opacity_effect)

        # Set initial states for crossfade
        self.current_opacity_effect.setOpacity(1.0)
        self.next_opacity_effect.setOpacity(0.0)

        # Position next widget exactly over current widget for perfect crossfade
        next_widget.setGeometry(current_widget.geometry())
        next_widget.show()
        next_widget.raise_()

        # Create crossfade animations with matching timing
        self.fade_out_anim = QPropertyAnimation(self.current_opacity_effect, b"opacity", self)
        self.fade_out_anim.setStartValue(1.0)
        self.fade_out_anim.setEndValue(0.0)
        self.fade_out_anim.setDuration(duration)
        self.fade_out_anim.setEasingCurve(QEasingCurve.Type.InOutQuad)  # Smooth crossfade curve

        self.fade_in_anim = QPropertyAnimation(self.next_opacity_effect, b"opacity", self)
        self.fade_in_anim.setStartValue(0.0)
        self.fade_in_anim.setEndValue(1.0)
        self.fade_in_anim.setDuration(duration)
        self.fade_in_anim.setEasingCurve(QEasingCurve.Type.InOutQuad)  # Matching curve for perfect crossfade

        # Group animations for simultaneous crossfade
        self.page_anim_group = QParallelAnimationGroup(self)
        self.page_anim_group.addAnimation(self.fade_out_anim)
        self.page_anim_group.addAnimation(self.fade_in_anim)

        def on_finished():
            self.stacked_widget.setCurrentIndex(next_index)
            current_widget.hide()
            # Clean up effects
            current_widget.setGraphicsEffect(None)
            next_widget.setGraphicsEffect(None)

        self.page_anim_group.finished.connect(on_finished)
        self.page_anim_group.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def animate_slide(self, current_index, next_index, duration, slide_from_left=False):
        current_widget = self.stacked_widget.widget(current_index)
        next_widget = self.stacked_widget.widget(next_index)
        if not current_widget or not next_widget:
            self.stacked_widget.setCurrentIndex(next_index)
            return
        width = self.stacked_widget.width()
        offset_x = -width if slide_from_left else width
        next_widget.setGeometry(self.stacked_widget.rect())
        next_widget.move(offset_x, 0); next_widget.show(); next_widget.raise_()
        anim_current_pos = QPropertyAnimation(current_widget, b"pos", self)
        anim_current_pos.setEndValue(QPoint(-offset_x, 0)); anim_current_pos.setDuration(duration)
        anim_current_pos.setEasingCurve(QEasingCurve.Type.OutQuart)
        anim_next_pos = QPropertyAnimation(next_widget, b"pos", self)
        anim_next_pos.setEndValue(QPoint(0, 0)); anim_next_pos.setDuration(duration)
        anim_next_pos.setEasingCurve(QEasingCurve.Type.OutQuart)
        self.page_anim_group = QParallelAnimationGroup(self)
        self.page_anim_group.addAnimation(anim_current_pos); self.page_anim_group.addAnimation(anim_next_pos)
        def on_animation_finished():
            self.stacked_widget.setCurrentIndex(next_index)
            current_widget.hide(); current_widget.move(0, 0)
        self.page_anim_group.finished.connect(on_animation_finished)
        self.page_anim_group.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def animate_slide_vertical(self, current_index, next_index, duration, slide_from_bottom=False):
        current_widget = self.stacked_widget.widget(current_index)
        next_widget = self.stacked_widget.widget(next_index)
        if not current_widget or not next_widget:
            self.stacked_widget.setCurrentIndex(next_index)
            return
        height = self.stacked_widget.height()
        offset_y = height if slide_from_bottom else -height
        next_widget.setGeometry(self.stacked_widget.rect())
        next_widget.move(0, offset_y); next_widget.show(); next_widget.raise_()
        anim_current_pos = QPropertyAnimation(current_widget, b"pos", self)
        anim_current_pos.setEndValue(QPoint(0, -offset_y)); anim_current_pos.setDuration(duration)
        anim_current_pos.setEasingCurve(QEasingCurve.Type.InOutCubic)
        anim_next_pos = QPropertyAnimation(next_widget, b"pos", self)
        anim_next_pos.setEndValue(QPoint(0, 0)); anim_next_pos.setDuration(duration)
        anim_next_pos.setEasingCurve(QEasingCurve.Type.InOutCubic)
        self.page_anim_group = QParallelAnimationGroup(self)
        self.page_anim_group.addAnimation(anim_current_pos); self.page_anim_group.addAnimation(anim_next_pos)
        def on_animation_finished():
            self.stacked_widget.setCurrentIndex(next_index)
            current_widget.hide(); current_widget.move(0, 0)
        self.page_anim_group.finished.connect(on_animation_finished)
        self.page_anim_group.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def create_header(self):
        header = QWidget(); header.setObjectName("headerWidget")
        s_margin = int(BASE_MARGIN * SCALE_FACTOR); s_spacing = int(BASE_SPACING * SCALE_FACTOR)
        layout = QHBoxLayout(header)
        # Add small right margin to compensate for 6px scrollbar
        scrollbar_compensation = int(6 * SCALE_FACTOR)
        layout.setContentsMargins(s_margin, int(10 * SCALE_FACTOR), s_margin + scrollbar_compensation, int(10 * SCALE_FACTOR))
        layout.setSpacing(s_spacing)
        # Add spacer for color circle column
        s_circle_size = int(BASE_CIRCLE_SIZE * SCALE_FACTOR)
        circle_spacer = QLabel(); circle_spacer.setFixedSize(s_circle_size, s_circle_size)
        brand_label = QLabel("Brand"); brand_label.setMinimumWidth(int(100 * SCALE_FACTOR)); brand_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        type_label = QLabel("Type"); type_label.setMinimumWidth(int(80 * SCALE_FACTOR)); type_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        color_label = QLabel("Color"); color_label.setMinimumWidth(int(100 * SCALE_FACTOR)); color_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        stock_label = QLabel("In Stock"); stock_label.setMinimumWidth(int(80 * SCALE_FACTOR)); stock_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        used_label = QLabel("Used"); used_label.setMinimumWidth(int(80 * SCALE_FACTOR)); used_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(circle_spacer, 0); layout.addWidget(brand_label, 1); layout.addWidget(type_label, 1)
        layout.addSpacing(int(40 * SCALE_FACTOR))  # Extra space between Type and Color
        layout.addWidget(color_label, 1)
        layout.addSpacing(int(60 * SCALE_FACTOR))  # Gap between Color and Used

        # Group Used, In Stock, Actions in a sub-layout for fine spacing
        right_group = QWidget()
        right_layout = QHBoxLayout(right_group)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(4)
        right_layout.addWidget(used_label, 1)
        right_layout.addWidget(stock_label, 1)
        right_layout.addStretch()
        actions_label = QLabel("Actions"); actions_label.setMinimumWidth(int(80 * SCALE_FACTOR)); actions_label.setAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
        right_layout.addWidget(actions_label)
        layout.addWidget(right_group, 3)
        return header

    def show_scrollbar_temporarily(self):
        """Show scrollbar and start hide timer"""
        self.scroll_area.verticalScrollBar().show()
        self.scrollbar_hide_timer.start(2000)  # Hide after 2 seconds

    def hide_scrollbar(self):
        """Hide the scrollbar"""
        self.scroll_area.verticalScrollBar().hide()

    def eventFilter(self, source, event):
        """Event filter to show scrollbar on wheel events"""
        if source == self.scroll_area and event.type() == QEvent.Type.Wheel:
            self.show_scrollbar_temporarily()
        return super().eventFilter(source, event)

    def create_action_button(self, icon_name, bg_color, obj_name, icon_color='white', icon_size=None):
        s_icon_size = int(icon_size * SCALE_FACTOR) if icon_size else int(BASE_ICON_SIZE * SCALE_FACTOR)
        s_btn_size = int(BASE_ACTION_BTN_SIZE * SCALE_FACTOR)
        button = QPushButton(); button.setObjectName(obj_name); button.setIcon(load_svg_icon(icon_name, icon_color))
        button.setIconSize(QSize(s_icon_size, s_icon_size)); button.setFixedSize(s_btn_size, s_btn_size)

        # Special handling for backButton - rounded rectangle theme-aware styling
        if obj_name == "backButton":
            theme = THEMES.get(self.settings.get("theme", "Dark Modern"), THEMES["Dark Modern"])
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme['surface']};
                    border-radius: {s_radius}px;
                    border: 2px solid {theme['border']};
                }}
                QPushButton:hover {{
                    background-color: {theme['hover']};
                    border-color: {theme['primary']};
                }}
                QPushButton:pressed {{
                    background-color: {theme['border']};
                    border-color: {theme['primary']};
                }}
            """)
        else:
            # Don't set inline styles - let the main stylesheet handle it via objectName
            pass
        return button

    def center_and_resize(self):
        try:
            screen = QApplication.primaryScreen();
            if not screen: return
            available_rect = screen.availableGeometry()
            new_width = int(available_rect.width() * 0.5); new_height = int(available_rect.height() * 0.5)
            self.resize(new_width, new_height); window_rect = self.frameGeometry()
            window_rect.moveCenter(available_rect.center()); self.move(window_rect.topLeft())
        except Exception as e:
            logger.warning(f"Could not center and resize window: {e}")

    def load_and_display_filaments(self):
        self.filaments = load_data()
        self.selected_filament_id = None

        # PERFORMANCE OPTIMIZATION: Hide scroll area during widget creation to prevent flashes
        if hasattr(self, 'scroll_area'):
            self.scroll_area.hide()

        # PERFORMANCE OPTIMIZATION: Clear existing widgets efficiently
        while self.rows_layout.count():
            item = self.rows_layout.takeAt(0)
            if item and item.widget():
                item.widget().deleteLater()
        self.row_widgets.clear()

        # PERFORMANCE OPTIMIZATION: Create widgets efficiently for small datasets
        # For datasets under 100 items, create all widgets at once to avoid popup flashes
        if len(self.filaments) <= 100:
            # Create all widgets at once - fast enough for small datasets
            for f in self.filaments:
                row_widget = FilamentRowWidget(f)
                row_widget.clicked.connect(self.on_row_clicked)
                row_widget.adjust_clicked.connect(self.open_adjust_dialog)
                row_widget.notes_clicked.connect(self.open_notes_dialog)
                self.row_widgets[f.id] = row_widget

            # Display them and show scroll area
            self.display_sorted_and_filtered_filaments()
            if hasattr(self, 'scroll_area'):
                self.scroll_area.show()
        else:
            # For larger datasets, use batched creation
            self._create_widgets_batch(self.filaments, 0)

    def _create_widgets_batch(self, filaments: list, start_index: int, batch_size: int = 20):
        """
        PERFORMANCE OPTIMIZATION: Create widgets in batches for large datasets only.
        Only used when filament count > 100 to avoid popup flashes on small datasets.
        """
        end_index = min(start_index + batch_size, len(filaments))

        # Create widgets for this batch
        for i in range(start_index, end_index):
            f = filaments[i]
            row_widget = FilamentRowWidget(f)
            row_widget.clicked.connect(self.on_row_clicked)
            row_widget.adjust_clicked.connect(self.open_adjust_dialog)
            row_widget.notes_clicked.connect(self.open_notes_dialog)
            self.row_widgets[f.id] = row_widget

        # If there are more widgets to create, schedule next batch
        if end_index < len(filaments):
            QTimer.singleShot(5, lambda: self._create_widgets_batch(filaments, end_index, batch_size))
        else:
            # All widgets created, now display them and show scroll area
            self.display_sorted_and_filtered_filaments()
            if hasattr(self, 'scroll_area'):
                self.scroll_area.show()

    def add_single_filament_widget(self, filament: Filament):
        """Add a single filament widget without recreating all widgets"""
        if filament.id not in self.row_widgets:
            row_widget = FilamentRowWidget(filament)
            row_widget.clicked.connect(self.on_row_clicked)
            row_widget.adjust_clicked.connect(self.open_adjust_dialog)
            row_widget.notes_clicked.connect(self.open_notes_dialog)
            self.row_widgets[filament.id] = row_widget
            # Re-sort and display to put it in the right position
            self.display_sorted_and_filtered_filaments()

    def update_single_filament_widget(self, filament: Filament):
        """Update a single filament widget with new data"""
        if filament.id in self.row_widgets:
            self.row_widgets[filament.id].update_data(filament)
            # Re-sort in case brand/type/color changed
            self.display_sorted_and_filtered_filaments()

    def display_sorted_and_filtered_filaments(self):
        if self.current_search_query:
            query = self.current_search_query.lower()
            filtered_filaments = [f for f in self.filaments if
                query in f.brand.lower() or query in f.type.lower() or query in f.color.lower()]
        else:
            filtered_filaments = self.filaments[:]
        key, is_desc = self.current_sort_key
        try:
            if filtered_filaments and isinstance(getattr(filtered_filaments[0], key, None), str):
                sorted_filaments = sorted(filtered_filaments, key=lambda f: getattr(f, key).lower(), reverse=is_desc)
            else:
                sorted_filaments = sorted(filtered_filaments, key=lambda f: getattr(f, key), reverse=is_desc)
        except (AttributeError, IndexError):
             sorted_filaments = filtered_filaments

        # PERFORMANCE OPTIMIZATION: Prevent visual flashes during layout updates
        # Hide the scroll area content while we reorganize widgets
        scroll_content = self.scroll_area.widget()
        if scroll_content:
            scroll_content.setUpdatesEnabled(False)

        # Hide all widgets first
        for widget in self.row_widgets.values():
            widget.hide()

        # Add widgets to layout in correct order
        for i, filament in enumerate(sorted_filaments):
            row_widget = self.row_widgets.get(filament.id)
            if row_widget:
                self.rows_layout.insertWidget(i, row_widget)
                row_widget.show()

        # Re-enable updates and refresh the display all at once
        if scroll_content:
            scroll_content.setUpdatesEnabled(True)
            scroll_content.update()

    def open_adjust_dialog(self, filament_id):
        filament_to_adjust = next((f for f in self.filaments if f.id == filament_id), None)
        if not filament_to_adjust: return
        sender_widget = self.sender()
        if not isinstance(sender_widget, FilamentRowWidget): return
        sender_button = sender_widget.adjust_button
        dialog = AdjustUsageDialog(f"{filament_to_adjust.brand} {filament_to_adjust.color}", self)
        button_pos = sender_button.mapToGlobal(sender_button.rect().bottomLeft())
        dialog.move(button_pos.x() - dialog.width() + sender_button.width(), button_pos.y())
        dialog.usage_logged.connect(partial(self.handle_usage_logged, filament_id))
        dialog.stock_added.connect(partial(self.handle_stock_added, filament_id)); dialog.exec()

    def open_notes_dialog(self, filament_id):
        filament = next((f for f in self.filaments if f.id == filament_id), None)
        if not filament: return

        # Simple notes dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Notes - {filament.brand} {filament.color}")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # Notes text area
        notes_text = QTextEdit()
        notes_text.setPlaceholderText("Add notes about this filament...")
        # Load existing notes
        notes_text.setText(filament.notes)
        layout.addWidget(notes_text)

        # Buttons
        button_layout = QHBoxLayout()
        cancel_btn = QPushButton("Cancel")
        save_btn = QPushButton("Save")
        cancel_btn.clicked.connect(dialog.reject)
        save_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        layout.addLayout(button_layout)

        if dialog.exec():
            # Actually save the notes to the filament object
            notes = notes_text.toPlainText()
            filament.notes = notes

            # Save the updated filament data to the JSON file
            save_data(self.filaments)

            show_custom_message(self, 'fa5s.check', '#28a745', "Notes Saved",
                               f"Notes saved for {filament.brand} {filament.color}")

    def handle_usage_logged(self, filament_id, amount_to_log):
        filament = next((f for f in self.filaments if f.id == filament_id), None)
        if not filament:
            return

        if amount_to_log > filament.in_stock_grams:
            show_custom_message(self, 'fa5s.exclamation-triangle', '#d9534f', "Error",
                               f"Not enough stock! Available: {filament.in_stock_grams}g, Required: {amount_to_log}g")
            return

        # Daten aktualisieren
        filament.in_stock_grams -= amount_to_log
        filament.used_grams += amount_to_log
        save_data(self.filaments)

        # Cache invalidieren da sich Filament-Daten geändert haben
        self.invalidate_cache()

        # UI aktualisieren
        self.update_single_row(filament_id, removed_amount=amount_to_log)
        self.update_notifications()

        # Shop page auch aktualisieren falls nötig
        if self.stacked_widget.currentIndex() == 2:
            self.update_shop_page()

    def handle_stock_added(self, filament_id, amount_to_add):
        filament = next((f for f in self.filaments if f.id == filament_id), None)
        if not filament:
            return

        # Daten aktualisieren
        filament.in_stock_grams += amount_to_add
        save_data(self.filaments)

        # Cache invalidieren da sich Filament-Daten geändert haben
        self.invalidate_cache()

        # UI aktualisieren
        self.update_single_row(filament_id, added_amount=amount_to_add)
        self.update_notifications()

        # Shop page auch aktualisieren falls nötig
        if self.stacked_widget.currentIndex() == 2:
            self.update_shop_page()

    def update_single_row(self, filament_id: str, added_amount: Optional[float] = None, removed_amount: Optional[float] = None):
        row_widget = self.row_widgets.get(filament_id)
        filament = next((f for f in self.filaments if f.id == filament_id), None)
        if not row_widget or not filament:
            return

        remaining_label = row_widget.findChild(HighlightableLabel, "inStockLabel")
        used_label = row_widget.findChild(HighlightableLabel, "usedLabel")

        if remaining_label:
            remaining_label.setText(f"{filament.in_stock_grams:g}g")
            if added_amount:
                remaining_label.flash(f"+{added_amount:g}g", "#28a745")
            elif removed_amount:
                remaining_label.flash(f"-{removed_amount:g}g", "#dc3545")

        if used_label:
            used_label.setText(f"{filament.used_grams:g}g")
            if removed_amount:
                used_label.flash(f"+{removed_amount:g}g", "#17a2b8")

        row_widget.update()
        self.display_sorted_and_filtered_filaments()

    def on_row_clicked(self, filament_id):
        if self.selected_filament_id == filament_id:
            if filament_id in self.row_widgets: self.row_widgets[filament_id].set_selected(False)
            self.selected_filament_id = None
        else:
            if self.selected_filament_id and self.selected_filament_id in self.row_widgets:
                self.row_widgets[self.selected_filament_id].set_selected(False)
            self.selected_filament_id = filament_id
            if filament_id in self.row_widgets: self.row_widgets[filament_id].set_selected(True)

    def add_filament(self):
        dialog = FilamentDialog(parent=self, currency_symbol=self.settings.get("currency_symbol", "€"))
        if not dialog.exec(): return
        data = dialog.get_data()
        if not all([data["brand"], data["color"], data["in_stock_grams"] > 0]):
            show_custom_message(self, 'fa5s.exclamation-circle', '#f0ad4e', "Input Error", "Brand, Color and a valid Stock amount are required.")
            return

        new_filament = Filament(**data)
        self.filaments.append(new_filament)
        save_data(self.filaments)

        # Optimized: Add single widget instead of recreating all
        self.add_single_filament_widget(new_filament)
        self.invalidate_cache()  # Invalidate cache for statistics

        # Only update other pages if currently viewing them
        if self.stacked_widget.currentIndex() == 1:
            self.update_settings_page()
        if self.stacked_widget.currentIndex() == 2:
            self.update_shop_page()

        self.update_notifications()

    def edit_filament(self):
        if not self.selected_filament_id:
            show_custom_message(self, 'fa5s.exclamation-circle', '#f0ad4e', "Selection Error", "Please select a filament to edit.")
            return
        filament_to_edit = next((f for f in self.filaments if f.id == self.selected_filament_id), None)
        if not filament_to_edit: return

        # Store original stock amount to detect changes
        original_stock = filament_to_edit.in_stock_grams

        dialog = FilamentDialog(filament=copy.deepcopy(filament_to_edit), parent=self, currency_symbol=self.settings.get("currency_symbol", "€"))
        if dialog.exec():
            data = dialog.get_data()
            new_stock = data["in_stock_grams"]

            # Calculate the difference in stock
            stock_difference = new_stock - original_stock

            filament_to_edit.brand = data["brand"]
            filament_to_edit.type = data["type"]
            filament_to_edit.color = data["color"]
            filament_to_edit.hex_color = data["hex_color"]
            filament_to_edit.purchase_price = data["purchase_price"]
            filament_to_edit.in_stock_grams = data["in_stock_grams"]
            filament_to_edit.empty_spool_weight = data["empty_spool_weight"]
            save_data(self.filaments)

            # Optimized: Update single widget instead of recreating all
            self.update_single_filament_widget(filament_to_edit)
            self.invalidate_cache()  # Invalidate cache for statistics

            # Only update other pages if currently viewing them
            if self.stacked_widget.currentIndex() == 1:
                self.update_settings_page()
            if self.stacked_widget.currentIndex() == 2:
                self.update_shop_page()

            self.update_notifications()

            # Show bubble animation if stock amount changed
            if stock_difference != 0:
                if stock_difference > 0:
                    # Stock was added
                    self.update_single_row(self.selected_filament_id, added_amount=stock_difference)
                else:
                    # Stock was removed
                    self.update_single_row(self.selected_filament_id, removed_amount=abs(stock_difference))

            # Update notifications after changes
            self.update_notifications()

    def delete_filament(self):
        if not self.selected_filament_id:
            show_custom_message(self, 'fa5s.exclamation-circle', '#f0ad4e', "Selection Error", "Please select a filament to delete.")
            return
        if not ask_custom_question(self, "Confirm Deletion", "Are you sure you want to delete this filament?"):
            return
        widget_to_delete = self.row_widgets.get(self.selected_filament_id)
        if not widget_to_delete: return
        self.animate_row_deletion(widget_to_delete)

    def animate_row_deletion(self, widget_to_delete):
        filament_id_to_delete = widget_to_delete.filament.id

        widget_to_delete.setMinimumHeight(widget_to_delete.height())

        size_anim = QPropertyAnimation(widget_to_delete, b"minimumHeight")
        size_anim.setDuration(300)
        size_anim.setEndValue(0)
        size_anim.setEasingCurve(QEasingCurve.Type.InOutCubic)

        fade_anim = QPropertyAnimation(widget_to_delete, b"windowOpacity")
        fade_anim.setDuration(300)
        fade_anim.setEndValue(0)
        fade_anim.setEasingCurve(QEasingCurve.Type.InOutCubic)

        anim_group = QParallelAnimationGroup(self)
        anim_group.addAnimation(size_anim)
        anim_group.addAnimation(fade_anim)

        def on_finished():
            widget_to_delete.hide()
            self.rows_layout.removeWidget(widget_to_delete)
            self.filaments = [f for f in self.filaments if f.id != filament_id_to_delete]
            save_data(self.filaments)
            self.update_settings_page()
            self.update_shop_page()
            self.update_notifications()
            if self.selected_filament_id == filament_id_to_delete:
                self.selected_filament_id = None
            if filament_id_to_delete in self.row_widgets:
                del self.row_widgets[filament_id_to_delete]
            widget_to_delete.deleteLater()

        anim_group.finished.connect(on_finished)
        anim_group.start(QAbstractAnimation.DeletionPolicy.DeleteWhenStopped)

    def open_log_print_dialog(self, product_id: str):
        # Force a reload of data from disk to prevent any possibility of using stale, in-memory data
        # which might be causing the persistent calculation bug in the history.
        self.filaments = load_data()
        self.products = load_products()

        product_to_log = next((p for p in self.products if p.id == product_id), None)
        if not product_to_log: return
        if not product_to_log.variations:
            show_custom_message(self, 'fa5s.info-circle', '#5bc0de', "No Variations", "This product has no variations defined. Please edit it to add one.")
            return
        dialog = LogPrintDialog(product_to_log, self)
        if dialog.exec():
            selected_variation = dialog.get_selected_variation()
            if selected_variation:
                # Handle Custom variation differently
                if selected_variation.name == "Custom":
                    # For manual variation, create a log entry without reducing stock
                    log_entry = PrintLogEntry(
                        product_id=product_to_log.id,
                        product_name=product_to_log.name,
                        product_image_name=product_to_log.image_name,
                        variation_name=selected_variation.name,
                        timestamp=datetime.now().isoformat(),
                        components=[]  # Empty components for manual variation
                    )
                    self.print_history.insert(0, log_entry)
                    save_print_history(self.print_history)

                    self.settings['products_printed'] = self.settings.get('products_printed', 0) + 1
                    save_settings(self.settings)

                    # Update UI
                    self.update_settings_page()
                    self.update_notifications()
                    if self.stacked_widget.currentIndex() != 0:
                        self.switch_page(0)
                    return

                # For regular variations, proceed with normal stock checking and reduction
                # First, check for sufficient stock to avoid partial logging
                for component in selected_variation.components:
                    filament = next((f for f in self.filaments if f.id == component.filament_id), None)
                    if not filament or filament.in_stock_grams < component.grams_used:
                        show_custom_message(self, 'fa5s.exclamation-triangle', '#d9534f', "Insufficient Stock",
                                            f"Not enough stock for {filament.brand if filament else 'N/A'} {filament.color if filament else ''}. "
                                            f"Required: {component.grams_used}g, Available: {filament.in_stock_grams if filament else 0}g.")
                        return # Stop before any data is changed

                # If all checks pass, proceed with logging
                used_components_for_log: list[UsedComponent] = []
                # --- NEU: Nur das erste Filament wird als Scroll-Ziel verwendet ---
                scroll_target_filament_id = None
                for idx, component in enumerate(selected_variation.components):
                    filament = next((f for f in self.filaments if f.id == component.filament_id), None)
                    if filament:
                        # Update filament stock
                        filament.in_stock_grams -= component.grams_used
                        filament.used_grams += component.grams_used

                        # Create a component entry for the log
                        price_per_kg = filament.purchase_price
                        grams = component.grams_used
                        cost_of_component = (price_per_kg / 1000.0) * grams

                        used_components_for_log.append(UsedComponent(
                            filament_brand=filament.brand,
                            filament_type=filament.type,
                            filament_color=filament.color,
                            hex_color=filament.hex_color,
                            grams_used=component.grams_used,
                            cost=cost_of_component
                        ))
                        # Visually update the corresponding row
                        if idx == 0:
                            scroll_target_filament_id = filament.id

                        self.update_single_row(filament.id, removed_amount=component.grams_used)

                # Nach allen Updates: explizit NUR zum ersten Filament scrollen (falls mehrere betroffen)
                if scroll_target_filament_id:
                    target_widget = self.row_widgets.get(scroll_target_filament_id)
                    if target_widget:
                        # Ensure the widget is visible after a short delay to allow the UI to update
                        QTimer.singleShot(100, lambda: self.scroll_area.ensureWidgetVisible(target_widget))

                # Create the main log entry
                log_entry = PrintLogEntry(
                    product_id=product_to_log.id,
                    product_name=product_to_log.name,
                    product_image_name=product_to_log.image_name,
                    variation_name=selected_variation.name,
                    timestamp=datetime.now().isoformat(),
                    components=used_components_for_log
                )
                self.print_history.insert(0, log_entry) # Prepend to history
                save_print_history(self.print_history)

                # Save other data
                save_data(self.filaments)
                self.settings['products_printed'] = self.settings.get('products_printed', 0) + 1
                save_settings(self.settings)

                # Update UI
                self.update_settings_page() # Make sure stats card updates immediately
                self.update_notifications()
                if self.stacked_widget.currentIndex() != 0:
                    self.switch_page(0)

    def on_search_text_changed(self, text):
        self.current_search_query = text.lower()
        self.display_sorted_and_filtered_filaments()

    def show_sort_menu(self):
        sort_menu = QMenu(self)
        brand_action = sort_menu.addAction("Sort by Brand")
        brand_action.triggered.connect(lambda: self.set_sort_order("brand"))
        color_action = sort_menu.addAction("Sort by Color")
        color_action.triggered.connect(lambda: self.set_sort_order("color"))
        sort_menu.addSeparator()
        stock_desc_action = sort_menu.addAction("Sort by In Stock (Most first)")
        stock_desc_action.triggered.connect(lambda: self.set_sort_order("in_stock_grams", True))
        stock_asc_action = sort_menu.addAction("Sort by In Stock (Least first)")
        stock_asc_action.triggered.connect(lambda: self.set_sort_order("in_stock_grams", False))
        sort_menu.addSeparator()
        used_desc_action = sort_menu.addAction("Sort by Used (Most first)")
        used_desc_action.triggered.connect(lambda: self.set_sort_order("used_grams", True))
        used_asc_action = sort_menu.addAction("Sort by Used (Least first)")
        used_asc_action.triggered.connect(lambda: self.set_sort_order("used_grams", False))
        sender_button = self.sender()
        if sender_button:
            menu_pos = sender_button.mapToGlobal(QPoint(0, sender_button.height()))
            sort_menu.exec(menu_pos)

    def set_sort_order(self, key, descending=False):
        self.current_sort_key = (key, descending)
        self.display_sorted_and_filtered_filaments()

    def _create_stat_card(self, icon_name: str, icon_color: str, title: str) -> tuple[QFrame, QLabel]:
        card = QFrame()
        card.setObjectName("statCard")
        s_icon_size = int(22 * SCALE_FACTOR)
        s_margin = int(15 * SCALE_FACTOR)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        layout.setSpacing(int(8 * SCALE_FACTOR))

        header_layout = QHBoxLayout()
        header_layout.setSpacing(int(10 * SCALE_FACTOR))

        icon_label = QLabel()
        icon = qta.icon(icon_name, color=icon_color)
        icon_label.setPixmap(icon.pixmap(QSize(s_icon_size, s_icon_size)))

        title_label = QLabel(title)
        title_label.setObjectName("statCardTitle")

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        value_label = QLabel("...")
        value_label.setObjectName("statValue")

        layout.addLayout(header_layout)
        layout.addWidget(value_label)

        return card, value_label

    def _create_stat_card_with_info(self, icon_name: str, icon_color: str, title: str, info_callback) -> tuple[QFrame, QLabel]:
        card = QFrame()
        card.setObjectName("statCard")
        s_icon_size = int(22 * SCALE_FACTOR)
        s_margin = int(15 * SCALE_FACTOR)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(s_margin, s_margin, s_margin, s_margin)
        layout.setSpacing(int(8 * SCALE_FACTOR))

        header_layout = QHBoxLayout()
        header_layout.setSpacing(int(10 * SCALE_FACTOR))

        icon_label = QLabel()
        icon = qta.icon(icon_name, color=icon_color)
        icon_label.setPixmap(icon.pixmap(QSize(s_icon_size, s_icon_size)))

        title_label = QLabel(title)
        title_label.setObjectName("statCardTitle")

        # Info button hinzufügen
        info_button = QPushButton()
        info_button.setIcon(qta.icon('fa5s.exclamation-circle', color='#B0B0B0'))
        info_button.setFixedSize(QSize(int(16 * SCALE_FACTOR), int(16 * SCALE_FACTOR)))
        info_button.setObjectName("transparentButton")
        info_button.setToolTip("More information")
        info_button.clicked.connect(info_callback)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(info_button)
        header_layout.addStretch()

        value_label = QLabel("...")
        value_label.setObjectName("statValue")

        layout.addLayout(header_layout)
        layout.addWidget(value_label)

        return card, value_label

    def show_length_info(self):
        """Shows an information popup for the Length Printed statistic."""
        popup = LengthInfoPopup(self)
        # Position the popup relative to the main window
        popup.show_centered(self)

    def show_print_history(self, a0: Optional[QMouseEvent] = None):
        dialog = self.PrintHistoryDialog(self.print_history, self.settings.get("currency_symbol", "€"), self)
        dialog.exec()

    def show_book_dialog(self):
        """Show the Log Book dialog window with filter functionality."""
        # Load fresh log book data
        log_book_data = load_log_book()
        dialog = self.BookDialog(self.print_history, log_book_data, self.settings.get("currency_symbol", "€"), self.filaments, self)
        dialog.exec()

    def show_app_info(self):
        """Show application information dialog."""
        dialog = QDialog(self)
        dialog.setWindowTitle("About HueStock")
        dialog.setFixedSize(int(420 * SCALE_FACTOR), int(480 * SCALE_FACTOR))
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: #2d2d2d;
                color: #ffffff;
                border-radius: {s_radius}px;
            }}
        """)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(int(8 * SCALE_FACTOR))
        layout.setContentsMargins(int(25 * SCALE_FACTOR), int(20 * SCALE_FACTOR), int(25 * SCALE_FACTOR), int(20 * SCALE_FACTOR))

        # App logo
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Use the helper function to get the correct appicon path
        icon_path = get_appicon_path()
        if icon_path.exists():
            pixmap = QPixmap(str(icon_path))
            # Scale to display size but maintain high pixel density
            display_size = int(320 * SCALE_FACTOR)  # 4x larger than before (80 * 4 = 320)
            # Use 3x the display size for internal rendering to get crisp pixels
            render_size = display_size * 3  # 3x supersampling for ultra-crisp rendering
            scaled_pixmap = pixmap.scaled(render_size, render_size,
                                        Qt.AspectRatioMode.KeepAspectRatio,
                                        Qt.TransformationMode.SmoothTransformation)
            # Set higher device pixel ratio for crisp display
            scaled_pixmap.setDevicePixelRatio(3.0)
            # Final scale to display size
            final_pixmap = scaled_pixmap.scaled(display_size, display_size,
                                              Qt.AspectRatioMode.KeepAspectRatio,
                                              Qt.TransformationMode.SmoothTransformation)
            icon_label.setPixmap(final_pixmap)
        else:
            app_icon = qta.icon('fa5s.boxes', color='#17a2b8')
            icon_label.setPixmap(app_icon.pixmap(QSize(int(320 * SCALE_FACTOR), int(320 * SCALE_FACTOR))))

        # Title section - centered
        app_title = QLabel("HueStock")
        app_title.setStyleSheet(f"font-size: {int(18 * SCALE_FACTOR)}pt; font-weight: bold; color: #17a2b8;")
        app_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        app_title.setWordWrap(True)

        author_text = QLabel("Made by Dzzylabs")
        author_text.setStyleSheet(f"font-size: {int(12 * SCALE_FACTOR)}pt; color: #b0b0b0;")
        author_text.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Version
        version_text = QLabel("Version 1.1")
        version_text.setStyleSheet(f"font-size: {int(14 * SCALE_FACTOR)}pt; font-weight: bold; color: #17a2b8;")
        version_text.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Free message
        free_text = QLabel("This app is 100% free to use. Enjoy!")
        free_text.setStyleSheet(f"font-size: {int(12 * SCALE_FACTOR)}pt; color: #28a745; font-weight: bold;")
        free_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        free_text.setWordWrap(True)

        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("color: #4a4a4a;")

        # Contact section
        contact_label = QLabel("Contact:")
        contact_label.setStyleSheet(f"font-size: {int(13 * SCALE_FACTOR)}pt; font-weight: bold; color: #17a2b8;")
        contact_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Website
        website_btn = QPushButton("🌐 www.dzzylabs.com")
        website_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                color: #17a2b8;
                font-size: {int(11 * SCALE_FACTOR)}pt;
                text-align: center;
                padding: 8px 16px;
                min-height: {int(25 * SCALE_FACTOR)}px;
            }}
            QPushButton:hover {{
                color: #138496;
                text-decoration: underline;
            }}
        """)
        website_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        website_btn.clicked.connect(lambda: __import__('webbrowser').open('https://www.dzzylabs.com'))

        # Email
        email_btn = QPushButton("✉️ <EMAIL>")
        email_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                color: #17a2b8;
                font-size: {int(11 * SCALE_FACTOR)}pt;
                text-align: center;
                padding: 8px 16px;
                min-height: {int(25 * SCALE_FACTOR)}px;
            }}
            QPushButton:hover {{
                color: #138496;
                text-decoration: underline;
            }}
        """)
        email_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        email_btn.clicked.connect(lambda: __import__('webbrowser').open('mailto:<EMAIL>'))

        # Donation section
        donate_btn = QPushButton("☕ Support the Development")
        donate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ffc107;
                color: #000000;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: {int(11 * SCALE_FACTOR)}pt;
                font-weight: bold;
                min-height: {int(30 * SCALE_FACTOR)}px;
                min-width: {int(180 * SCALE_FACTOR)}px;
            }}
            QPushButton:hover {{
                background-color: #e0a800;
            }}
        """)
        donate_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        donate_btn.clicked.connect(lambda: __import__('webbrowser').open('https://ko-fi.com/dzzylabs'))



        # Add everything to layout - all centered with reduced spacing
        layout.addWidget(icon_label)
        layout.addSpacing(int(5 * SCALE_FACTOR))
        layout.addWidget(app_title)
        layout.addWidget(author_text)
        layout.addSpacing(int(8 * SCALE_FACTOR))
        layout.addWidget(version_text)
        layout.addWidget(free_text)
        layout.addWidget(separator)
        layout.addSpacing(int(6 * SCALE_FACTOR))
        layout.addWidget(contact_label)
        layout.addWidget(website_btn)
        layout.addWidget(email_btn)
        layout.addSpacing(int(8 * SCALE_FACTOR))
        layout.addWidget(donate_btn)
        layout.addStretch()

        # Apply current theme
        dialog.setStyleSheet(self.styleSheet())

        dialog.exec()

    class BookDialog(QDialog):
        def __init__(self, history: list['PrintLogEntry'], log_book_data: list, currency_symbol: str, filaments: list, parent=None):
            super().__init__(parent)
            self.setWindowTitle("Log Book")
            self.history = history
            self.log_book_data = log_book_data
            self.currency_symbol = currency_symbol
            self.filaments = filaments
            self.main_window = parent  # Reference to main window

            # Same responsive sizing as PrintHistoryDialog
            self.setMinimumSize(400, 200)

            # Image cache for product images (same as PrintHistoryDialog)
            self.image_cache = {}

            # Filter state
            self.current_filter = "all"  # "all", "logged", "added", "products"

            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(int(8 * SCALE_FACTOR), int(8 * SCALE_FACTOR),
                                         int(8 * SCALE_FACTOR), int(8 * SCALE_FACTOR))
            main_layout.setSpacing(int(6 * SCALE_FACTOR))

            # Header with title and filter button
            header_layout = QHBoxLayout()

            title = QLabel("Log Book")
            title.setObjectName("titleLabel")
            header_layout.addWidget(title)

            header_layout.addStretch()

            # Filter button (same style as main window)
            self.filter_button = QPushButton()
            self.filter_button.setIcon(load_svg_icon('list-filter-plus', '#B0B0B0'))
            self.filter_button.setFixedSize(QSize(int(40 * SCALE_FACTOR), int(40 * SCALE_FACTOR)))
            self.filter_button.setObjectName("filterButton")
            self.filter_button.clicked.connect(self.show_filter_menu)
            header_layout.addWidget(self.filter_button)

            main_layout.addLayout(header_layout)

            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setObjectName("scrollArea")
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

            content_widget = QWidget()
            self.history_layout = QVBoxLayout(content_widget)
            self.history_layout.setContentsMargins(int(8 * SCALE_FACTOR), 0, int(8 * SCALE_FACTOR), 0)
            self.history_layout.setSpacing(int(4 * SCALE_FACTOR))
            self.history_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

            # Populate the initial display
            self.populate_display()

            scroll_area.setWidget(content_widget)
            main_layout.addWidget(scroll_area, 1)

        def show_filter_menu(self):
            """Show filter menu for log book entries."""
            menu = QMenu(self)

            # Add filter options
            all_action = menu.addAction("Show All")
            all_action.setCheckable(True)
            all_action.setChecked(self.current_filter == "all")
            all_action.triggered.connect(lambda: self.apply_filter("all"))

            logged_action = menu.addAction("Show Logged Only")
            logged_action.setCheckable(True)
            logged_action.setChecked(self.current_filter == "logged")
            logged_action.triggered.connect(lambda: self.apply_filter("logged"))

            added_action = menu.addAction("Show Added Only")
            added_action.setCheckable(True)
            added_action.setChecked(self.current_filter == "added")
            added_action.triggered.connect(lambda: self.apply_filter("added"))

            products_action = menu.addAction("Show Products Only")
            products_action.setCheckable(True)
            products_action.setChecked(self.current_filter == "products")
            products_action.triggered.connect(lambda: self.apply_filter("products"))

            # Show menu at filter button position
            button_pos = self.filter_button.mapToGlobal(self.filter_button.rect().bottomLeft())
            menu.exec(button_pos)

        def apply_filter(self, filter_type):
            """Apply the selected filter and refresh display."""
            self.current_filter = filter_type
            self.populate_display()





        def populate_display(self):
            """Populate the display with current data, respecting the current filter."""
            # Clear existing widgets first
            for i in reversed(range(self.history_layout.count())):
                child = self.history_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            # Combine and sort all entries by timestamp
            all_entries = []

            # Add print history entries (if filter allows)
            if self.current_filter in ["all", "products"]:
                for entry in self.history:
                    all_entries.append({
                        'type': 'print_history',
                        'timestamp': entry.timestamp,
                        'data': entry
                    })

            # Add log book entries (if filter allows)
            if self.current_filter in ["all", "logged", "added"]:
                for entry in self.log_book_data:
                    entry_type = entry.get('type', 'usage_logged')

                    # Apply specific log book filters
                    if self.current_filter == "logged" and entry_type != "usage_logged":
                        continue
                    elif self.current_filter == "added" and entry_type != "stock_added":
                        continue

                    all_entries.append({
                        'type': 'log_book',
                        'timestamp': entry.get('timestamp', ''),
                        'data': entry
                    })

            # Sort by timestamp (newest first)
            all_entries.sort(key=lambda x: x['timestamp'], reverse=True)

            if not all_entries:
                filter_text = {
                    "all": "No entries logged yet",
                    "logged": "No usage entries logged yet",
                    "added": "No stock additions logged yet",
                    "products": "No products printed yet"
                }.get(self.current_filter, "No entries found")

                no_entries_label = QLabel(filter_text)
                no_entries_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.history_layout.addWidget(no_entries_label)
            else:
                for entry_wrapper in all_entries:
                    if entry_wrapper['type'] == 'print_history':
                        # Display print history entry
                        entry = entry_wrapper['data']
                        pixmap = None
                        if entry.product_image_name:
                            if entry.product_image_name in self.image_cache:
                                pixmap = self.image_cache[entry.product_image_name]
                            else:
                                image_path = os.path.join(PRODUCTS_DIR, entry.product_image_name)
                                if os.path.exists(image_path):
                                    pixmap = create_cropped_pixmap(image_path, int(48 * SCALE_FACTOR), int(48 * SCALE_FACTOR), radius=6)
                                    self.image_cache[entry.product_image_name] = pixmap
                        entry_widget = PrintHistoryEntryWidget(entry, self.currency_symbol, pixmap)
                        # Add blue-ish background for print history entries
                        entry_widget.setStyleSheet("""
                            QFrame[objectName="historyEntryCard"] {
                                background-color: rgba(23, 162, 184, 0.15);
                                border: 1px solid rgba(23, 162, 184, 0.3);
                                border-radius: 6px;
                            }
                        """)
                        self.history_layout.addWidget(entry_widget)
                    elif entry_wrapper['type'] == 'log_book':
                        # Display log book entry
                        entry = entry_wrapper['data']
                        log_widget = self.create_log_book_entry_widget(entry)
                        self.history_layout.addWidget(log_widget)

        def create_log_book_entry_widget(self, entry):
            """Create a widget for log book entries."""
            from datetime import datetime

            widget = QFrame()
            widget.setObjectName("historyEntryCard")

            # Set background color based on entry type
            entry_type = entry.get('type', 'usage_logged')
            if entry_type == 'usage_logged':
                # Red-ish background for logged usage
                widget.setStyleSheet("""
                    QFrame[objectName="historyEntryCard"] {
                        background-color: rgba(220, 53, 69, 0.15);
                        border: 1px solid rgba(220, 53, 69, 0.3);
                        border-radius: 6px;
                    }
                """)
            elif entry_type == 'stock_added':
                # Green-ish background for added stock
                widget.setStyleSheet("""
                    QFrame[objectName="historyEntryCard"] {
                        background-color: rgba(40, 167, 69, 0.15);
                        border: 1px solid rgba(40, 167, 69, 0.3);
                        border-radius: 6px;
                    }
                """)
            else:
                # Default gray background for other types
                widget.setStyleSheet("""
                    QFrame[objectName="historyEntryCard"] {
                        background-color: rgba(108, 117, 125, 0.15);
                        border: 1px solid rgba(108, 117, 125, 0.3);
                        border-radius: 6px;
                    }
                """)

            padding = int(6 * SCALE_FACTOR)
            main_layout = QHBoxLayout(widget)
            main_layout.setSpacing(int(8 * SCALE_FACTOR))
            main_layout.setContentsMargins(padding, padding, padding, padding)
            widget.setFixedWidth(int(600 * SCALE_FACTOR))

            # Icon for log book entry based on type
            icon_label = QLabel()
            icon_label.setFixedSize(int(48 * SCALE_FACTOR), int(48 * SCALE_FACTOR))
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            if entry_type == 'usage_logged':
                icon_label.setStyleSheet(f"""
                    QLabel {{
                        background-color: #dc3545;
                        border-radius: 6px;
                        border: 1px solid rgba(255, 255, 255, 0.12);
                    }}
                """)
                # Use package-minus SVG icon
                pixmap = load_svg_icon('package-minus', 'white').pixmap(int(32 * SCALE_FACTOR), int(32 * SCALE_FACTOR))
                icon_label.setPixmap(pixmap)
            elif entry_type == 'stock_added':
                icon_label.setStyleSheet(f"""
                    QLabel {{
                        background-color: #28a745;
                        border-radius: 6px;
                        border: 1px solid rgba(255, 255, 255, 0.12);
                    }}
                """)
                # Use package-plus SVG icon
                pixmap = load_svg_icon('package-plus', 'white').pixmap(int(32 * SCALE_FACTOR), int(32 * SCALE_FACTOR))
                icon_label.setPixmap(pixmap)
            else:
                icon_label.setStyleSheet(f"""
                    QLabel {{
                        background-color: #6c757d;
                        border-radius: 6px;
                        border: 1px solid rgba(255, 255, 255, 0.12);
                        color: white;
                        font-size: {int(16 * SCALE_FACTOR)}pt;
                        qproperty-alignment: AlignCenter;
                    }}
                """)
                icon_label.setText("📝")  # Default icon

            main_layout.addWidget(icon_label)

            # Info column
            info_col = QVBoxLayout()
            info_col.setSpacing(int(2 * SCALE_FACTOR))

            # Top row: Description | Date
            top_row = QHBoxLayout()
            top_row.setSpacing(int(8 * SCALE_FACTOR))

            description = entry.get('description', 'Manual usage logged')
            filament_name = entry.get('filament_name', 'Unknown')
            name_label = QLabel(f"<b>{description}</b> - {filament_name}")
            name_label.setObjectName("historyProductTitle")
            name_label.setTextFormat(Qt.TextFormat.RichText)
            top_row.addWidget(name_label, 1)

            try:
                log_date = datetime.fromisoformat(entry.get('timestamp', '')).strftime('%d.%m.%y %H:%M')
            except ValueError:
                log_date = "Unknown"
            date_label = QLabel(log_date)
            date_label.setObjectName("historyDate")
            date_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            top_row.addWidget(date_label, 0)
            info_col.addLayout(top_row)

            # Bottom row: Action amount
            bottom_row = QHBoxLayout()
            bottom_row.setSpacing(int(8 * SCALE_FACTOR))

            grams_used = entry.get('grams_used', 0)
            if entry_type == 'usage_logged':
                action_label = QLabel(f"Manual usage: {grams_used:g}g")
            elif entry_type == 'stock_added':
                action_label = QLabel(f"Stock added: {grams_used:g}g")
            else:
                action_label = QLabel(f"Manual action: {grams_used:g}g")

            action_label.setObjectName("historyFilamentLabel")
            action_label.setWordWrap(True)
            bottom_row.addWidget(action_label, 1)

            # Empty space for alignment with print history
            bottom_row.addWidget(QLabel(""), 0)
            info_col.addLayout(bottom_row)

            main_layout.addLayout(info_col)
            return widget

        def resizeEvent(self, event):
            """Resize event to match PrintHistoryDialog layout exactly."""
            # Passe die Breite der Einträge an die aktuelle Fensterbreite an
            content_width = self.width() - 2 * int(8 * SCALE_FACTOR)
            for i in range(self.history_layout.count()):
                item = self.history_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    widget.setFixedWidth(max(300, content_width - 16))  # Small buffer for equal spacing
            super().resizeEvent(event)

    class PrintHistoryDialog(QDialog):
        def __init__(self, history: list['PrintLogEntry'], currency_symbol: str, parent=None):
            super().__init__(parent)
            self.setWindowTitle("Print History")

            # Responsive: Keine feste Mindest-/Maximalgröße, aber sinnvolles Minimum
            self.setMinimumSize(400, 200)

            # --- Bild-Cache für Produktbilder ---
            self.image_cache = {}

            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(int(8 * SCALE_FACTOR), int(8 * SCALE_FACTOR),
                                         int(8 * SCALE_FACTOR), int(8 * SCALE_FACTOR))
            main_layout.setSpacing(int(6 * SCALE_FACTOR))

            title = QLabel("Print History")
            title.setObjectName("titleLabel")
            main_layout.addWidget(title)

            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setObjectName("scrollArea")
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

            content_widget = QWidget()
            self.history_layout = QVBoxLayout(content_widget)
            self.history_layout.setContentsMargins(int(8 * SCALE_FACTOR), 0, int(8 * SCALE_FACTOR), 0)
            self.history_layout.setSpacing(int(4 * SCALE_FACTOR))
            self.history_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

            if not history:
                no_prints_label = QLabel("No prints logged yet")
                no_prints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.history_layout.addWidget(no_prints_label)
            else:
                for entry in history:
                    # --- Bild-Cache verwenden ---
                    pixmap = None
                    if entry.product_image_name:
                        if entry.product_image_name in self.image_cache:
                            pixmap = self.image_cache[entry.product_image_name]
                        else:
                            image_path = os.path.join(PRODUCTS_DIR, entry.product_image_name)
                            if os.path.exists(image_path):
                                pixmap = create_cropped_pixmap(image_path, int(48 * SCALE_FACTOR), int(48 * SCALE_FACTOR), radius=6)
                                self.image_cache[entry.product_image_name] = pixmap
                    entry_widget = PrintHistoryEntryWidget(entry, currency_symbol, pixmap)
                    self.history_layout.addWidget(entry_widget)

            scroll_area.setWidget(content_widget)
            main_layout.addWidget(scroll_area, 1)

        def resizeEvent(self, event):
            # Passe die Breite der Einträge an die aktuelle Fensterbreite an
            content_width = self.width() - 2 * int(8 * SCALE_FACTOR)
            for i in range(self.history_layout.count()):
                item = self.history_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    widget.setFixedWidth(max(300, content_width - 16))  # Small buffer for equal spacing
            super().resizeEvent(event)

    def closeEvent(self, event):
        """Handle application close event"""
        # Accept the close event immediately for fast shutdown
        event.accept()

        # Stop servers in background with a slight delay
        # No servers to stop in desktop-only mode
        pass

class PrintHistoryEntryWidget(QFrame):
    def __init__(self, entry: 'PrintLogEntry', currency_symbol: str, pixmap=None, parent=None):
        super().__init__(parent)
        self.setObjectName("historyEntryCard")

        padding = int(6 * SCALE_FACTOR)
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(int(8 * SCALE_FACTOR))
        main_layout.setContentsMargins(padding, padding, padding, padding)
        self.setFixedWidth(int(600 * SCALE_FACTOR))  # Breite des Widgets auf 600 * SCALE_FACTOR setzen

        # Bild (links)
        image_label = QLabel()
        image_label.setFixedSize(int(48 * SCALE_FACTOR), int(48 * SCALE_FACTOR))
        if pixmap:
            image_label.setPixmap(pixmap)
        main_layout.addWidget(image_label)

        # Rechte Info-Spalte
        info_col = QVBoxLayout()
        info_col.setSpacing(int(2 * SCALE_FACTOR))

        # Erste Zeile: Produktname - Variation | Datum/Uhrzeit (rechts)
        top_row = QHBoxLayout()
        top_row.setSpacing(int(8 * SCALE_FACTOR))
        name_label = QLabel(f"<b>{entry.product_name}</b> - {entry.variation_name}")
        name_label.setObjectName("historyProductTitle")
        # Font size will be handled by the dynamic stylesheet
        name_label.setTextFormat(Qt.TextFormat.RichText)
        top_row.addWidget(name_label, 1)

        try:
            print_date = datetime.fromisoformat(entry.timestamp).strftime('%d.%m.%y %H:%M')
        except ValueError:
            print_date = "Unknown"
        date_label = QLabel(print_date)
        date_label.setObjectName("historyDate")
        # Font size and color will be handled by the dynamic stylesheet
        date_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        top_row.addWidget(date_label, 0)
        info_col.addLayout(top_row)

        # Zweite Zeile: Filamentliste | Total (rechts)
        bottom_row = QHBoxLayout()
        bottom_row.setSpacing(int(8 * SCALE_FACTOR))
        components_summary = []
        total_cost = 0
        for component in entry.components:
            components_summary.append(f"{component.filament_brand} {component.filament_color}: {component.grams_used:g}g")
            total_cost += component.cost
        filaments_label = QLabel(", ".join(components_summary))
        filaments_label.setObjectName("historyFilamentLabel")
        # Font size will be handled by the dynamic stylesheet
        filaments_label.setWordWrap(True)
        bottom_row.addWidget(filaments_label, 1)

        total_label = QLabel(f"Total: {total_cost:.2f} {currency_symbol}")
        total_label.setObjectName("historyCostLabel")
        # Font size and weight will be handled by the dynamic stylesheet
        total_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        bottom_row.addWidget(total_label, 0)
        info_col.addLayout(bottom_row)

        main_layout.addLayout(info_col)

class NotificationPopup(QDialog):
    dismiss_requested = Signal(object)

    def __init__(self, notifications: list['Notification'], parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setObjectName("notificationPopup")
        self.installEventFilter(self)
        self.setMinimumWidth(int(400 * SCALE_FACTOR))

        # Setup animation for smooth appearance
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(200)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)

        if not notifications:
            layout.addWidget(QLabel("No new notifications."))
        else:
            title = QLabel("Notifications")
            title.setObjectName("subTitleLabel")
            layout.addWidget(title)
            for notification in notifications:
                icon_name = 'fa5s.exclamation-triangle'
                icon_color = '#d9534f' if notification.level == "red" else '#f0ad4e'

                line = QFrame()
                line_layout = QHBoxLayout(line)
                line_layout.setContentsMargins(0,0,0,0)
                line_layout.setSpacing(10)

                icon = qta.icon(icon_name, color=icon_color)
                icon_label = QLabel()
                icon_label.setPixmap(icon.pixmap(QSize(int(14*SCALE_FACTOR), int(14*SCALE_FACTOR))))

                msg_label = QLabel(notification.message)
                msg_label.setWordWrap(True)

                dismiss_btn = QPushButton("Dismiss")
                dismiss_btn.setObjectName("dismissButton")
                dismiss_btn.clicked.connect(lambda checked=False, bound_notification=notification: self.safe_dismiss(bound_notification))

                line_layout.addWidget(icon_label)
                line_layout.addWidget(msg_label, 1)
                line_layout.addWidget(dismiss_btn)
                layout.addWidget(line)

    def safe_dismiss(self, notification):
        """Safely emit dismiss signal with error handling"""
        try:
            self.dismiss_requested.emit(notification)
        except RuntimeError as e:
            logger.warning(f"Error emitting dismiss signal: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in dismiss: {e}")

    def eventFilter(self, source, event):
        if event.type() == QEvent.Type.WindowDeactivate:
            self.close()
        return super().eventFilter(source, event)

    def show_at_with_animation(self, pos: QPoint):
        # First, determine the final size without showing
        self.adjustSize()
        final_width = self.width()
        final_height = self.height()

        # Position popup next to the button (to the right), aligned with top
        final_x = pos.x()
        final_y = pos.y()

        # Ensure we don't go off screen
        screen = QApplication.primaryScreen()
        if screen:
            screen_rect = screen.availableGeometry()
            if final_x + final_width > screen_rect.right():
                final_x = screen_rect.right() - final_width
            if final_y + final_height > screen_rect.bottom():
                final_y = screen_rect.bottom() - final_height

        # Set final geometry and show without animation to avoid geometry conflicts
        final_rect = QRect(final_x, final_y, final_width, final_height)
        self.setGeometry(final_rect)
        self.show()

        # Use opacity animation instead of geometry animation to avoid Windows conflicts
        self.setWindowOpacity(0.0)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()

    def show_at(self, pos: QPoint):
        # Keep old method for compatibility, but use new animation
        self.show_at_with_animation(pos)

class DismissDialog(QDialog):
    def __init__(self, notification: 'Notification', parent=None):
        super().__init__(parent)
        self.notification = notification
        self.setWindowTitle("Dismiss Notification")
        self.setMinimumWidth(int(400 * SCALE_FACTOR))

        layout = QVBoxLayout(self)
        layout.setSpacing(int(15 * SCALE_FACTOR))

        title_label = QLabel("Dismiss Notification")
        title_label.setObjectName("subTitleLabel")
        layout.addWidget(title_label)

        info_text = f"<p><b>Hide alert for:</b><br>{notification.message}</p>"
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        options_frame = QFrame()
        options_frame.setObjectName("optionsFrame")
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(int(10 * SCALE_FACTOR))

        self.change_radio = QRadioButton("Dismiss until stock changes")
        self.days_radio = QRadioButton("Dismiss for a number of days:")
        self.change_radio.setChecked(True)

        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)
        self.days_spinbox.setValue(7)

        days_row_layout = QHBoxLayout()
        days_row_layout.setContentsMargins(0, 0, 0, 0)
        days_row_layout.addWidget(self.days_radio)
        days_row_layout.addStretch()
        days_row_layout.addWidget(self.days_spinbox)

        self.days_radio.toggled.connect(self.days_spinbox.setEnabled)
        self.days_spinbox.setEnabled(False)

        options_layout.addWidget(self.change_radio)
        options_layout.addLayout(days_row_layout)
        layout.addWidget(options_frame)

        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def get_data(self) -> Optional[dict[str, Any]]:
        base_data: dict[str, Any] = {
            'type': self.notification.type,
            'id': self.notification.id,
            'payload_at_dismiss': self.notification.payload
        }
        # --- Erweiterung für Produkt-Notifications: Komponenten-Stocks speichern ---
        if self.notification.type == "product" and self.change_radio.isChecked():
            try:
                # Hole aktuelle Filament-Stocks für alle Komponenten
                from PySide6.QtWidgets import QApplication
                mw = None
                for w in QApplication.topLevelWidgets():
                    if hasattr(w, "filaments") and hasattr(w, "products"):
                        mw = w
                        break
                component_stocks = []
                if mw and hasattr(mw, 'products') and hasattr(mw, 'filaments'):
                    try:
                        product = next((p for p in mw.products if p.id == self.notification.id), None)
                        if product and hasattr(product, 'variations'):
                            for variation in product.variations:
                                if hasattr(variation, 'components'):
                                    for component in variation.components:
                                        if hasattr(component, 'filament_id'):
                                            filament = next((f for f in mw.filaments if f.id == component.filament_id), None)
                                            if filament and hasattr(filament, 'in_stock_grams'):
                                                component_stocks.append({
                                                    "filament_id": filament.id,
                                                    "in_stock_grams": filament.in_stock_grams
                                                })
                    except (StopIteration, AttributeError, TypeError) as e:
                        logger.warning(f"Error getting component stocks: {e}")
                base_data["component_stocks_at_dismiss"] = component_stocks
            except Exception as e:
                logger.error(f"Error in product dismissal data collection: {e}")
                base_data["component_stocks_at_dismiss"] = []
        if self.days_radio.isChecked():
            days = self.days_spinbox.value()
            if days > 0:
                dismiss_until = datetime.now() + timedelta(days=days)
                base_data['dismiss_until'] = dismiss_until.isoformat()
                return base_data
        elif self.change_radio.isChecked():
            base_data['dismiss_until_change'] = True
            return base_data
        return None

    def _remove_filament_dismissal(self, filament_id: str):
        original_len = len(self.settings['dismissed_notifications'])
        self.settings['dismissed_notifications'] = [
            d for d in self.settings['dismissed_notifications']
            if not (d.get('type') == 'filament' and d.get('id') == filament_id)
        ]
        if len(self.settings['dismissed_notifications']) != original_len:
            save_settings(self.settings)



# --- LengthInfoPopup als eigenständige Klasse ---
class LengthInfoPopup(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Length Printed - Distance Comparison")
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        self.setObjectName("lengthInfoPopup")

        layout = QVBoxLayout(self)
        layout.setSpacing(int(15 * SCALE_FACTOR))
        layout.setContentsMargins(int(25 * SCALE_FACTOR), int(25 * SCALE_FACTOR),
                                 int(25 * SCALE_FACTOR), int(25 * SCALE_FACTOR))

        # Calculate total length
        filaments = load_data()
        total_used_grams = sum(f.used_grams for f in filaments)
        METERS_PER_KG = 335  # 1kg of 1.75mm filament = 335 meters
        total_length_meters = (total_used_grams / 1000.0) * METERS_PER_KG

        # Basic info with rich text
        basic_info = QLabel()
        basic_info.setText(f'<span style="font-size: {int(16 * SCALE_FACTOR)}pt; color: #17a2b8; font-weight: bold;">Total printed: {total_length_meters:.1f} meters</span>')
        basic_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(basic_info)

        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("border: 1px solid #4A4A4A; margin: 10px 0px;")
        layout.addWidget(separator)

        # Distance comparison
        emoji, comparison_text, times_text = self.get_distance_comparison(total_length_meters)

        # Emoji
        emoji_label = QLabel(emoji)
        emoji_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        emoji_label.setStyleSheet(f"font-size: {int(32 * SCALE_FACTOR)}pt; margin: 10px 0px;")
        layout.addWidget(emoji_label)

        # Comparison text
        comparison_label = QLabel(comparison_text)
        comparison_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        comparison_label.setObjectName("comparisonText")
        comparison_label.setWordWrap(True)
        layout.addWidget(comparison_label)

        # Times text (big and bold)
        times_label = QLabel(times_text)
        times_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        times_label.setObjectName("timesText")
        times_label.setWordWrap(True)
        layout.addWidget(times_label)

        layout.addStretch()

        # Auto-adjust size after content is set
        self.adjustSize()

        # Set minimum size based on content
        self.setMinimumSize(self.size())

    def get_distance_comparison(self, meters: float) -> tuple[str, str, str]:
        """Get the best distance comparison for the printed length."""
        if meters == 0:
            return "🎯", "You haven't printed anything yet!", ""

        # Distance comparisons (in meters)
        comparisons = [
            (40075000, "🌍", "Earth's circumference"),
            (384400000, "🌙", "Distance to the Moon"),
            (225000000000, "🚀", "Distance to Mars"),
            (778500000000, "🪐", "Distance to Jupiter"),
            (149597870700, "☀️", "Distance to the Sun"),
            (8848, "🏔️", "Mount Everest height"),
            (828, "🏢", "Burj Khalifa height"),
            (443, "🗽", "Empire State Building height"),
            (324, "🗼", "Eiffel Tower height"),
            (42195, "🏃", "Marathon distance"),
            (100, "⚽", "Football field length"),
            (2.4, "🚗", "Average car length"),
        ]

        # Find the best comparison
        for distance, emoji, name in comparisons:
            ratio = meters / distance

            if ratio >= 1:
                if ratio >= 1000:
                    return emoji, name, f"{ratio:.0f} times!"
                elif ratio >= 100:
                    return emoji, name, f"{ratio:.0f} times!"
                elif ratio >= 10:
                    return emoji, name, f"{ratio:.1f} times!"
                elif ratio >= 2:
                    return emoji, name, f"{ratio:.1f} times!"
                else:
                    return emoji, name, "Once!"

        # If nothing matches, use the smallest comparison
        smallest_distance, smallest_emoji, smallest_name = comparisons[-1]
        ratio = meters / smallest_distance
        return smallest_emoji, smallest_name, f"{ratio:.1f} times!"

    def show_centered(self, parent):
        """Show the popup centered on the parent window."""
        self.show()
        if parent:
            parent_rect = parent.geometry()
            popup_rect = self.geometry()
            x = parent_rect.x() + (parent_rect.width() - popup_rect.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - popup_rect.height()) // 2
            self.move(x, y)

# --- Skalierte Werte für das QSS Stylesheet ---
s_title_font_size = int(18 * SCALE_FACTOR); s_main_font_size = int(BASE_FONT_SIZE * SCALE_FACTOR)
s_radius = int(BASE_RADIUS * SCALE_FACTOR); s_circle_radius = int(BASE_CIRCLE_SIZE * SCALE_FACTOR) // 2
s_action_btn_radius = s_radius; s_row_action_btn_radius = s_radius
s_padding = int(5 * SCALE_FACTOR); s_btn_padding_h = int(15 * SCALE_FACTOR); s_btn_padding_v = int(8 * SCALE_FACTOR)
s_control_radius = s_radius
s_filter_btn_radius = s_radius

# --- QSS Stylesheet ---
STYLESHEET = f"""
    QMainWindow, QDialog {{ background-color: #2D2D2D; font-family: 'Segoe UI'; font-weight: bold; }}
    #titleLabel {{ font-size: {s_title_font_size}pt; font-weight: bold; color: white; }}
    #headerWidget {{ background-color: #3D3D3D; border-radius: {s_radius}px; color: #B0B0B0; font-weight: bold; font-size: {s_main_font_size}pt; }}
    #listFrame {{ background-color: #3D3D3D; border-radius: {s_radius}px; }}
    #scrollArea, #scrollArea > QWidget > QWidget {{ background-color: transparent; border: none; }}
    #FilamentRow {{ background-color: transparent; color: #E0E0E0; font-size: {s_main_font_size}pt; }}
    #FilamentRow[selected="true"] {{
        background-color: #454545;
        border-radius: {s_radius - 3}px;
        margin: 1px 1px 1px 14px;
    }}
    #colorCircle, #warningCircle {{ border-radius: 6px; border: 1px solid rgba(255, 255, 255, 0.12); }}
    #addButton, #editButton, #deleteButton, #historyButton {{ border-radius: {s_action_btn_radius}px; border: none; }}
    #settingsButton {{ background-color: #0072BB; border-radius: {s_action_btn_radius}px; border: none; }}
    #shopButton {{ background-color: #1E91D6; border-radius: {s_action_btn_radius}px; border: none; }}
    #notificationButton {{ background-color: #50A0D1; border-radius: {s_action_btn_radius}px; border: none; }}
    QPushButton[objectName="backButton"] {{ background-color: #3D3D3D !important; border-radius: {s_radius}px !important; border: 2px solid #555 !important; }}
    QPushButton[objectName="backButton"]:hover {{ background-color: #4A4A4A !important; border-color: #17a2b8 !important; }}
    QPushButton[objectName="backButton"]:pressed {{ background-color: #555 !important; border-color: #17a2b8 !important; }}
    QPushButton[objectName="backButton"]:focus {{ background-color: #3D3D3D !important; border-color: #17a2b8 !important; outline: none; }}
    #adjustButton {{ background-color: #4A4A4A; border-radius: {s_row_action_btn_radius}px; }}
    #adjustButton:hover {{ background-color: #5A5A5A; }}
    #notesButton {{ background-color: #4A4A4A; border-radius: {s_row_action_btn_radius}px; }}
    #notesButton:hover {{ background-color: #5A5A5A; }}
    #transparentButton {{ background-color: transparent; border: none; }}
    #statsBox {{ margin-bottom: 2px; }}
    #totalUsageLabel {{ font-size: {int(36 * SCALE_FACTOR)}pt; font-weight: bold; color: white; }}
    #funFactLabel {{ font-size: {int(12 * SCALE_FACTOR)}pt; color: #B0B0B0; font-style: italic; }}
    #subTitleLabel {{ font-size: {int(11*SCALE_FACTOR)}pt; font-weight: bold; color: #E0E0E0; }}
    QLabel[class="topRankLabel"] {{ font-weight: bold; color: #B0B0B0; }}
    #rightColumnContainer {{ margin-top: 15px; }}
    #productTile {{ background-color: #3D3D3D; border-radius: 8px; border: 2px solid transparent; }}
    #productTile[selected="true"] {{ border-color: white; }}
    #productImage {{ background-color: transparent; }}
    #productTitle {{ padding: 5px; font-size: {s_main_font_size-1}pt; background-color: #3D3D3D; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; }}
    #variationFrame {{ border: 1px solid #4A4A4A; border-radius: 5px; margin-top: 10px; padding: 5px; }}
    #logPrintButton {{ background-color: rgba(0, 0, 0, 0.5); border: 1px solid rgba(255, 255, 255, 0.7); border-radius: 20px; }}
    #logPrintButton:hover {{ background-color: rgba(0, 0, 0, 0.7); }}
    #confirmButton {{ background-color: #28a745; }}
    #infoLabel {{ color: #B0B0B0; font-style: italic; font-size: {s_main_font_size - 2}pt; }}

    /* Modern Calculator Styling */
    #modernCalculator {{
        background-color: #2D2D2D;
        border: 2px solid #4A4A4A;
        border-radius: 15px;
    }}
    #modernDisplay {{
        background-color: #1E1E1E;
        border: 1px solid #4A4A4A;
        border-radius: 6px;
        padding: 8px;
        font-size: {int(14 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: #ffffff;
    }}
    #numButton {{
        background-color: #4A4A4A;
        border: 1px solid #555555;
        border-radius: 6px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: #ffffff;
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #numButton:hover {{ background-color: #5A5A5A; }}
    #numButton:pressed {{ background-color: #3A3A3A; }}
    #clearButton {{
        background-color: #e74c3c;
        border: 1px solid #c0392b;
        border-radius: 8px;
        font-size: {int(16 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #clearButton:hover {{ background-color: #c0392b; }}
    #backButton {{
        background-color: #f39c12;
        border: 1px solid #e67e22;
        border-radius: 8px;
        font-size: {int(16 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #backButton:hover {{ background-color: #e67e22; }}
    #stockButton {{
        background-color: #27ae60;
        border: 1px solid #229954;
        border-radius: 8px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #stockButton:hover {{ background-color: #229954; }}
    #stockButton:disabled {{ background-color: #7f8c8d; border-color: #95a5a6; }}
    #usageButton {{
        background-color: #e74c3c;
        border: 1px solid #c0392b;
        border-radius: 8px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    #usageButton:hover {{ background-color: #c0392b; }}
    #usageButton:disabled {{ background-color: #7f8c8d; border-color: #95a5a6; }}
    #commaButton {{
        background-color: #4A4A4A;
        border: 1px solid #555555;
        border-radius: 6px;
        font-size: {int(12 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: #ffffff;
        font-family: "Segoe UI", Arial, sans-serif;
        padding: 8px;
    }}
    #commaButton:hover {{ background-color: #5A5A5A; }}
    #commaButton:pressed {{ background-color: #3A3A3A; }}
    #addValueButton {{ background-color: #28a745; }}
    #removeValueButton {{ background-color: #555; }}
    QComboBox {{
        background-color: #3D3D3D;
        color: white;
        border: 1px solid #555;
        border-radius: {s_padding}px;
        padding: {s_padding}px;
        padding-left: {s_padding + 5}px;
        font-weight: bold;
    }}
    QComboBox::drop-down {{
        border: 0px;
        width: 0px;
    }}
    QComboBox QAbstractItemView {{
        background-color: #3D3D3D;
        border: 1px solid #555;
        color: white;
        selection-background-color: #555;
    }}
    QLineEdit, QSpinBox, QDoubleSpinBox {{
        background-color: #3D3D3D;
        color: white;
        border: 1px solid #555;
        border-radius: {s_padding}px;
        padding: {s_padding}px;
        font-size: {s_main_font_size}pt;
        font-weight: bold;
    }}
    QLineEdit#currencyInput {{
        text-align: center;
    }}
    QLabel {{ color: #E0E0E0; font-size: {s_main_font_size}pt; font-weight: bold; }}
    QPushButton {{ background-color: #555; color: white; font-weight: bold; border-radius: {s_padding}px; padding: {s_btn_padding_v}px {s_btn_padding_h}px; }}
    QPushButton:hover {{ background-color: #666; }}
    QCheckBox {{ color: #E0E0E0; font-size: {s_main_font_size}pt; font-weight: bold; }}
    QCheckBox::indicator {{ width: 15px; height: 15px; border: 1px solid #B0B0B0; border-radius: 4px;}}
    QCheckBox::indicator:checked {{ background-color: #17a2b8; border: 1px solid #17a2b8; }}
    #filterButton {{ background-color: transparent; border: 1px solid #5A5A5A; border-radius: {s_filter_btn_radius}px; }}
    #filterButton:hover {{ background-color: #4A4A4A; }}
    QLineEdit#search_input {{ padding-left: 10px; border-radius: {s_control_radius}px; }}

    QLineEdit#currencyInputCapsule {{
        border-radius: {s_radius}px;
    }}
    #themeCombo {{ border-radius: {s_radius}px; min-width: 120px; }}
    #infoButton {{
        background-color: #3D3D3D;
        border: 1px solid #555;
        border-radius: {s_radius}px;
    }}
    #infoButton:hover {{ background-color: #4A4A4A; }}
    #fontCombo {{ border-radius: {s_radius}px; min-width: 100px; }}
    QTextEdit#notesField {{
        background-color: #3D3D3D; /* Die gleiche Farbe wie #listFrame */
        color: white;
        border: none; /* Keinen eigenen Rand mehr! */
        border-radius: {s_radius}px;
        padding: {s_padding}px;
        font-size: {s_main_font_size}pt;
        font-weight: normal;
    }}

    /* --- Dashboard Styles --- */
    #dashboardCard, #statCard {{
        background-color: #3D3D3D;
        border-radius: {s_radius}px;
    }}
    #statCardTitle {{
        font-size: {s_main_font_size}pt;
        color: #B0B0B0;
        font-weight: bold;
    }}
    #statValue {{
        font-size: {int(28 * SCALE_FACTOR)}pt;
        font-weight: bold;
        color: white;
    }}
    QTextEdit#notesFieldDashboard {{
        background-color: transparent;
        color: white;
        border: none;
        padding: 0px;
        font-size: {s_main_font_size}pt;
        font-weight: normal;
    }}
    #historyEntryCard {{ background-color: #4A4A4A; border-radius: {s_radius}px; padding: 10px; }}
    #historyProductTitle {{ font-size: {s_main_font_size+2}pt; font-weight: bold; }}
    #historyDate {{ color: #B0B0B0; font-size: {s_main_font_size-1}pt; }}
    #historyCostLabel {{ font-weight: bold; font-size: {s_main_font_size}pt; }}
    #historyFilamentLabel {{ color: #E0E0E0; font-size: {s_main_font_size-1}pt; }}
    #historyImage {{ background-color: #3D3D3D; border-radius: {s_radius}px; }}
    #notificationPopup {{ background-color: #2D2D2D; border: 1px solid #555; border-radius: {s_radius}px; }}
    QPushButton#dismissButton {{ font-size: {s_main_font_size - 2}pt; padding: 2px 8px; background-color: #4A4A4A; }}
    QPushButton#dismissButton:hover {{ background-color: #5A5A5A; }}
    #optionsFrame {{ border: 1px solid #4A4A4A; border-radius: {s_radius}px; padding: 10px; }}
    #lengthInfoPopup {{ background-color: #2D2D2D; border: 1px solid #555; border-radius: {s_radius}px; }}
        #lengthBasicInfo {{
        font-size: {int(14 * SCALE_FACTOR)}pt;
        color: #17a2b8;
        padding: 10px;
        background-color: #3D3D3D;
        border-radius: {s_radius}px;
    }}
    #comparisonText {{
        font-size: {int(14 * SCALE_FACTOR)}pt;
        color: #E0E0E0;
        font-weight: bold;
        margin: 10px 0px;
    }}
    #timesText {{
        font-size: {int(20 * SCALE_FACTOR)}pt;
        color: #28a745;
        font-weight: bold;
        margin: 10px 0px;
        padding: 5px;
    }}
"""


if __name__ == "__main__":
    # Simple main app startup (splash is handled by launcher.py)
    QApplication.setAttribute(Qt.ApplicationAttribute(9), True)  # AA_EnableHighDpiScaling
    QApplication.setAttribute(Qt.ApplicationAttribute(10), True)  # AA_UseHighDpiPixmaps
    app = QApplication(sys.argv)

    # Load data and create main window directly (no splash - handled by launcher)
    preloaded_data = {
        'settings': load_settings(),
        'filaments': load_data(),
        'products': load_products(),
        'print_history': load_print_history()
    }

    # Create main window with preloaded data
    window = MainWindow(preloaded_data=preloaded_data)
    window.setObjectName("mainWidget")

    # Apply theme
    current_theme = window.settings.get("theme", "Dark Modern")
    window.setStyleSheet(get_theme_stylesheet(current_theme))

    # Show main window
    window.show()
    window.raise_()
    window.activateWindow()

    sys.exit(app.exec())
