import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QTableWidget, QTableWidgetItem, QPushButton,
                               QComboBox, QDoubleSpinBox, QSpinBox, QLabel, QHeaderView,
                               QMessageBox, QFileDialog, QLineEdit)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPalette, QColor
import json


class ItemCostTracker(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Filament Stock Tracker - Item Costs")
        self.setGeometry(100, 100, 1600, 1000)

        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTableWidget {
                background-color: #3c3c3c;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #4a4a4a;
            }
            QTableWidget::item {
                padding: 5px;
                border: 1px solid #555555;
            }
            QTableWidget::item:selected {
                background-color: #4a4a4a;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QComboBox, QDoubleSpinBox, QSpinBox, QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
                border-radius: 2px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #404040;
            }
            QComboBox::down-arrow {
                border: 2px solid #ffffff;
                border-top: none;
                border-right: none;
                width: 6px;
                height: 6px;
                margin-right: 8px;
            }
            QLabel {
                color: #ffffff;
            }
        """)

        # Load data from data.txt
        self.load_data()

        # Setup UI
        self.setup_ui()

    def load_data(self):
        """Load dropdown data from data.txt file"""
        self.storefronts = []
        self.box_weights = []
        self.additional_materials = {}

        try:
            with open('data.txt', 'r', encoding='utf-8') as file:
                content = file.read()

            sections = content.split('###')

            for section in sections:
                if 'STORE FRONTS' in section:
                    lines = section.strip().split('\n')[1:]  # Skip header
                    self.storefronts = [line.strip() for line in lines if line.strip()]

                elif 'BOX WEIGHTS' in section:
                    lines = section.strip().split('\n')[1:]  # Skip header
                    self.box_weights = [line.strip() for line in lines if line.strip()]

                elif 'ADDITIONAL MATERIALS' in section:
                    lines = section.strip().split('\n')[1:]  # Skip header
                    for line in lines:
                        if line.strip() and '=' in line:
                            name, cost = line.split('=')
                            # Extract cost value and convert to float
                            cost_value = float(cost.strip().replace('€', '').replace(',', '.'))
                            self.additional_materials[name.strip()] = cost_value

        except FileNotFoundError:
            QMessageBox.warning(self, "Warning", "data.txt file not found. Using default values.")
            self.storefronts = ["ETSY", "FOURTHWALL"]
            self.box_weights = ["12g", "80g", "87g", "145g"]
            self.additional_materials = {
                "6mm Magnet": 0.10,
                "Anti-Slip Pad": 0.015,
                "Keychain": 0.09,
                "White Bag": 0.10
            }

    def setup_ui(self):
        """Setup the main user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # Add title
        title_label = QLabel("Item Cost Calculator")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Create table
        self.table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.table)

        # Add buttons
        button_layout = QHBoxLayout()

        add_row_btn = QPushButton("Add Row")
        add_row_btn.clicked.connect(self.add_row)
        button_layout.addWidget(add_row_btn)

        delete_row_btn = QPushButton("Delete Selected Row")
        delete_row_btn.clicked.connect(self.delete_row)
        button_layout.addWidget(delete_row_btn)

        save_btn = QPushButton("Save Data")
        save_btn.clicked.connect(self.save_data)
        button_layout.addWidget(save_btn)

        load_btn = QPushButton("Load Data")
        load_btn.clicked.connect(self.load_saved_data)
        button_layout.addWidget(load_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # Add initial row
        self.add_row()

    def setup_table(self):
        """Setup the table with columns"""
        columns = [
            "Product Name", "Storefront", "Filament Cost (€)", "Add. Material Used",
            "Print Time (min)", "Item Weight (g)", "Extra Weight (g)", "Box Weight",
            "Shipping Costs DE (€)", "Shipping Costs INT (€)", "Total Weight (g)",
            "Total Production Cost (€)", "Total Price DE (€)", "Total Price INT (€)"
        ]

        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # Set minimum column widths for better readability
        for i in range(len(columns)):
            self.table.setColumnWidth(i, 120)

    def add_row(self):
        """Add a new row to the table"""
        row_position = self.table.rowCount()
        self.table.insertRow(row_position)

        # Product Name - Text input
        product_name = QLineEdit()
        product_name.textChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 0, product_name)

        # Storefront - Dropdown
        storefront_combo = QComboBox()
        storefront_combo.addItems(self.storefronts)
        storefront_combo.currentTextChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 1, storefront_combo)

        # Filament Cost - Number input
        filament_cost = QDoubleSpinBox()
        filament_cost.setRange(0, 999.99)
        filament_cost.setDecimals(2)
        filament_cost.setSuffix(" €")
        filament_cost.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 2, filament_cost)

        # Additional Material - Dropdown
        material_combo = QComboBox()
        material_combo.addItem("None")
        material_combo.addItems(list(self.additional_materials.keys()))
        material_combo.currentTextChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 3, material_combo)

        # Print Time - Number input
        print_time = QSpinBox()
        print_time.setRange(0, 9999)
        print_time.setSuffix(" min")
        print_time.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 4, print_time)

        # Item Weight - Number input
        item_weight = QDoubleSpinBox()
        item_weight.setRange(0, 9999.99)
        item_weight.setDecimals(1)
        item_weight.setSuffix(" g")
        item_weight.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 5, item_weight)

        # Extra Weight - Number input (default 20g)
        extra_weight = QDoubleSpinBox()
        extra_weight.setRange(0, 9999.99)
        extra_weight.setDecimals(1)
        extra_weight.setValue(20.0)  # Default to 20g
        extra_weight.setSuffix(" g")
        extra_weight.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 6, extra_weight)

        # Box Weight - Dropdown
        box_weight_combo = QComboBox()
        box_weight_combo.addItems(self.box_weights)
        box_weight_combo.currentTextChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 7, box_weight_combo)

        # Shipping Costs DE - Number input
        shipping_de = QDoubleSpinBox()
        shipping_de.setRange(0, 999.99)
        shipping_de.setDecimals(2)
        shipping_de.setSuffix(" €")
        shipping_de.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 8, shipping_de)

        # Shipping Costs INT - Number input
        shipping_int = QDoubleSpinBox()
        shipping_int.setRange(0, 999.99)
        shipping_int.setDecimals(2)
        shipping_int.setSuffix(" €")
        shipping_int.valueChanged.connect(lambda: self.calculate_costs(row_position))
        self.table.setCellWidget(row_position, 9, shipping_int)

        # Calculated fields (read-only labels)
        for col in [10, 11, 12, 13]:  # Total Weight, Total Production Cost, Total Price DE, Total Price INT
            label = QLabel("0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("background-color: #2a2a2a; border: 1px solid #555555; padding: 4px;")
            self.table.setCellWidget(row_position, col, label)

        # Initial calculation
        self.calculate_costs(row_position)

    def delete_row(self):
        """Delete the selected row"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            self.table.removeRow(current_row)
        else:
            QMessageBox.information(self, "Info", "Please select a row to delete.")

    def calculate_costs(self, row):
        """Calculate costs for a specific row"""
        try:
            # Get values from widgets
            filament_cost = self.table.cellWidget(row, 2).value()
            material_name = self.table.cellWidget(row, 3).currentText()
            print_time = self.table.cellWidget(row, 4).value()
            item_weight = self.table.cellWidget(row, 5).value()
            extra_weight = self.table.cellWidget(row, 6).value()
            box_weight_text = self.table.cellWidget(row, 7).currentText()
            shipping_de = self.table.cellWidget(row, 8).value()
            shipping_int = self.table.cellWidget(row, 9).value()

            # Extract box weight value (remove 'g' suffix)
            box_weight = float(box_weight_text.replace('g', '')) if box_weight_text else 0

            # Calculate additional material cost
            material_cost = 0
            if material_name != "None" and material_name in self.additional_materials:
                material_cost = self.additional_materials[material_name]

            # Calculate total weight
            total_weight = item_weight + extra_weight + box_weight

            # Calculate total production cost
            # Assuming some basic cost calculation - you can adjust this formula
            labor_cost = (print_time / 60) * 15  # 15€ per hour labor cost
            total_production_cost = filament_cost + material_cost + labor_cost

            # Calculate total prices (production cost + shipping)
            total_price_de = total_production_cost + shipping_de
            total_price_int = total_production_cost + shipping_int

            # Update calculated fields
            self.table.cellWidget(row, 10).setText(f"{total_weight:.1f} g")  # Total Weight
            self.table.cellWidget(row, 11).setText(f"{total_production_cost:.2f} €")  # Total Production Cost
            self.table.cellWidget(row, 12).setText(f"{total_price_de:.2f} €")  # Total Price DE
            self.table.cellWidget(row, 13).setText(f"{total_price_int:.2f} €")  # Total Price INT

        except (AttributeError, ValueError, TypeError):
            # Handle cases where widgets might not be fully initialized
            pass

    def save_data(self):
        """Save table data to a JSON file"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Save Data", "item_costs.json", "JSON Files (*.json)"
            )

            if filename:
                data = []
                for row in range(self.table.rowCount()):
                    row_data = {
                        'product_name': self.table.cellWidget(row, 0).text(),
                        'storefront': self.table.cellWidget(row, 1).currentText(),
                        'filament_cost': self.table.cellWidget(row, 2).value(),
                        'additional_material': self.table.cellWidget(row, 3).currentText(),
                        'print_time': self.table.cellWidget(row, 4).value(),
                        'item_weight': self.table.cellWidget(row, 5).value(),
                        'extra_weight': self.table.cellWidget(row, 6).value(),
                        'box_weight': self.table.cellWidget(row, 7).currentText(),
                        'shipping_de': self.table.cellWidget(row, 8).value(),
                        'shipping_int': self.table.cellWidget(row, 9).value()
                    }
                    data.append(row_data)

                with open(filename, 'w', encoding='utf-8') as file:
                    json.dump(data, file, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "Success", f"Data saved to {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save data: {str(e)}")

    def load_saved_data(self):
        """Load table data from a JSON file"""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "Load Data", "", "JSON Files (*.json)"
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as file:
                    data = json.load(file)

                # Clear existing rows
                self.table.setRowCount(0)

                # Load data
                for row_data in data:
                    self.add_row()
                    row = self.table.rowCount() - 1

                    # Set values
                    self.table.cellWidget(row, 0).setText(row_data.get('product_name', ''))

                    # Set storefront
                    storefront_combo = self.table.cellWidget(row, 1)
                    index = storefront_combo.findText(row_data.get('storefront', ''))
                    if index >= 0:
                        storefront_combo.setCurrentIndex(index)

                    self.table.cellWidget(row, 2).setValue(row_data.get('filament_cost', 0))

                    # Set additional material
                    material_combo = self.table.cellWidget(row, 3)
                    index = material_combo.findText(row_data.get('additional_material', 'None'))
                    if index >= 0:
                        material_combo.setCurrentIndex(index)

                    self.table.cellWidget(row, 4).setValue(row_data.get('print_time', 0))
                    self.table.cellWidget(row, 5).setValue(row_data.get('item_weight', 0))
                    self.table.cellWidget(row, 6).setValue(row_data.get('extra_weight', 20))

                    # Set box weight
                    box_combo = self.table.cellWidget(row, 7)
                    index = box_combo.findText(row_data.get('box_weight', ''))
                    if index >= 0:
                        box_combo.setCurrentIndex(index)

                    self.table.cellWidget(row, 8).setValue(row_data.get('shipping_de', 0))
                    self.table.cellWidget(row, 9).setValue(row_data.get('shipping_int', 0))

                    # Recalculate costs
                    self.calculate_costs(row)

                QMessageBox.information(self, "Success", f"Data loaded from {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load data: {str(e)}")


def main():
    app = QApplication(sys.argv)

    # Set application style to dark
    app.setStyle('Fusion')

    window = ItemCostTracker()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()